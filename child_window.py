import tkinter as tk
from tkinter import ttk
from playwright_utils import sync_playwright

class ChildWindow(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Управление браузером")
        self.master.geometry("400x300")
        self.pack()
        self.create_widgets()

    def create_widgets(self):
        self.url_label = ttk.Label(self, text="Введите URL:")
        self.url_label.pack(pady=10)

        self.url_entry = ttk.Entry(self, width=40)
        self.url_entry.pack()
        self.url_entry.insert(0, "https://example.com")

        self.run_btn = ttk.Button(
            self, 
            text="Запустить браузер", 
            command=self.run_playwright
        )
        self.run_btn.pack(pady=20)

    def run_playwright(self):
        """Запускает браузер через Playwright."""
        url = self.url_entry.get()
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            page = browser.new_page()
            page.goto(url)
            print(f"Загружена страница: {page.title()}")
            # Держим окно открытым (или закрываем по условию)
            input("Нажмите Enter для закрытия браузера...")
            browser.close()