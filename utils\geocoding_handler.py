import json
from pathlib import Path

class GeocodingHandler:
    def __init__(self, api_key):
        self.cache_file = Path("geocoding_cache.json")
        self.cache = self.load_cache()
        self.retry_attempts = 3
        self.retry_delay = 1  # секунды

    def load_cache(self):
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def save_cache(self):
        with open(self.cache_file, 'w') as f:
            json.dump(self.cache, f)

    def geocode_with_retry(self, address):
        if address in self.cache:
            return self.cache[address]

        # Логика повторных попыток будет дополнена в основном классе
        return (None, None)

# Модификация метода geocode_address в классе MapApp
def geocode_address(self, address):
    """Улучшенное геокодирование с повторными попытками и кэшированием"""
    if not address:
        return None, None

    # Проверка кэша
    if address in self.geocoding_cache:
        return self.geocoding_cache[address]

    attempts = 0
    services = ['google', 'nominatim']
    
    while attempts < self.retry_attempts:
        for service in services:
            try:
                if service == 'google' and self.gmaps_client:
                    time.sleep(self.retry_delay)
                    geocode_result = self.gmaps_client.geocode(
                        address, 
                        region='ru',
                        components={'country': 'RU'}
                    )
                    if geocode_result:
                        location = geocode_result[0]['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        self.geocoding_cache[address] = (lat, lng)
                        return lat, lng
                
                elif service == 'nominatim' and self.nominatim_client:
                    time.sleep(self.retry_delay * 2)  # Большая задержка для Nominatim
                    location = self.nominatim_client.geocode(
                        f"{address}, Россия",
                        addressdetails=True
                    )
                    if location:
                        self.geocoding_cache[address] = (location.latitude, location.longitude)
                        return location.latitude, location.longitude
                        
            except Exception as e:
                self.log_error(f"Ошибка {service}: {str(e)}")
        
        attempts += 1
        self.retry_delay *= 2  # Экспоненциальная задержка

    # Если все попытки провалились, предлагаем ручное исправление
    return self.manual_geocode_correction(address)

def log_error(self, message):
    """Логирование ошибок в статусную строку"""
    self.status_bar.config(text=message)
    self.status_bar.update_idletasks()

def manual_geocode_correction(self, address):
    """Окно ручного ввода координат"""
    dialog = tk.Toplevel(self.root)
    dialog.title("Ручное исправление координ��т")
    
    ttk.Label(dialog, text=f"Адрес: {address}").pack(pady=5)
    
    lat_frame = ttk.Frame(dialog)
    lat_frame.pack(pady=5)
    ttk.Label(lat_frame, text="Широта:").pack(side=tk.LEFT)
    lat_entry = ttk.Entry(lat_frame)
    lat_entry.pack(side=tk.LEFT)
    
    lng_frame = ttk.Frame(dialog)
    lng_frame.pack(pady=5)
    ttk.Label(lng_frame, text="Долгота:").pack(side=tk.LEFT)
    lng_entry = ttk.Entry(lng_frame)
    lng_entry.pack(side=tk.LEFT)
    
    result = []
    
    def save_coords():
        try:
            lat = float(lat_entry.get())
            lng = float(lng_entry.get())
            result.extend([lat, lng])
            self.geocoding_cache[address] = (lat, lng)
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Ошибка", "Введите числовые значения координат")
    
    ttk.Button(dialog, text="Сохранить", command=save_coords).pack(pady=10)
    
    dialog.wait_window()
    return result if result else (None, None)