import pandas as pd
import googlemaps
import time
import os
from tqdm import tqdm

def geocode_addresses(input_file, api_key):
    """
    Обрабатывает Excel-файл с адресами, добавляет координаты через Google Maps API
    и сохраняет необработанные адреса в отдельный файл.
    
    :param input_file: Путь к Excel-файлу с адресами
    :param api_key: Ключ Google Maps API
    :return: None
    """
    # Загрузка данных из Excel
    try:
        df = pd.read_excel(input_file)
    except Exception as e:
        print(f"Ошибка при чтении файла: {e}")
        return
    
    # Проверка структуры файла
    if len(df.columns) < 1:
        print("Файл должен содержать хотя бы один столбец с адресами")
        return
    
    address_col = df.columns[0]  # Первый столбец с адресами
    
    # Добавляем колонки для координат, если их нет
    if 'Широта' not in df.columns:
        df['Широта'] = None
    if 'Долгота' not in df.columns:
        df['Долгота'] = None
    
    # Инициализация Google Maps клиента
    gmaps = googlemaps.Client(key=api_key)
    
    # Списки для результатов
    success_addresses = []
    failed_addresses = []
    
    # Обработка адресов с прогресс-баром
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Обработка адресов"):
        address = row[address_col]
        
        if pd.isna(address) or str(address).strip() == '':
            continue
        
        try:
            # Геокодирование адреса
            geocode_result = gmaps.geocode(address)
            
            if geocode_result:
                location = geocode_result[0]['geometry']['location']
                df.at[index, 'Широта'] = location['lat']
                df.at[index, 'Долгота'] = location['lng']
                success_addresses.append(address)
            else:
                failed_addresses.append(address)
            
            # Ограничение API (50 запросов в секунду)
            time.sleep(0.02)
            
        except Exception as e:
            print(f"Ошибка при обработке адреса '{address}': {e}")
            failed_addresses.append(address)
    
    # Сохранение результатов
    output_file = os.path.splitext(input_file)[0] + "_with_coordinates.xlsx"
    df.to_excel(output_file, index=False)
    print(f"\nРезультаты сохранены в: {output_file}")
    
    # Сохранение необработанных адресов
    if failed_addresses:
        failed_df = pd.DataFrame({address_col: failed_addresses})
        failed_file = os.path.splitext(input_file)[0] + "_failed_addresses.xlsx"
        failed_df.to_excel(failed_file, index=False)
        print(f"Необработанные адресы сохранены в: {failed_file}")
        print(f"Количество необработанных адресов: {len(failed_addresses)}")
    
    print(f"Успешно обработано адресов: {len(success_addresses)}")

if __name__ == "__main__":
    # Пример использования
    input_excel = "test_file.xlsx"  # Путь к вашему файлу
    api_key = "AIzaSyBikxnCmNgmoerGPe0UOk5NukKfyd7EmC0"  # Ваш ключ API 
    
    geocode_addresses(input_excel, api_key)