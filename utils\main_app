# main_app.py
import tkinter as tk
from tkinter import ttk, messagebox
from utils.geocoding_handler import GeocodingHandler

class MapApp:
    def __init__(self, root):
        self.root = root
        self.geocoding_handler = GeocodingHandler(google_api_key="YOUR_API_KEY")
        
        # Инициализация GUI
        self.setup_ui()
        
    def setup_ui(self):
        self.status_bar = ttk.Label(self.root, text="Готово", relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def geocode_address(self, address):
        lat, lng = self.geocoding_handler.geocode(address)
        if not lat or not lng:
            return self.manual_geocode_correction(address)
        return lat, lng

    def manual_geocode_correction(self, address):
        dialog = tk.Toplevel(self.root)
        dialog.title("Ручное исправление координат")
        
        ttk.Label(dialog, text=f"Адрес: {address}").pack(pady=5)
        
        lat_entry = self._create_coord_entry(dialog, "Широта:")
        lng_entry = self._create_coord_entry(dialog, "Долгота:")
        
        result = []
        
        ttk.Button(dialog, text="Сохранить", 
                 command=lambda: self._save_coords(dialog, address, lat_entry, lng_entry, result)
        ).pack(pady=10)
        
        dialog.wait_window()
        return result if result else (None, None)

    def _create_coord_entry(self, parent, label):
        frame = ttk.Frame(parent)
        frame.pack(pady=5)
        ttk.Label(frame, text=label).pack(side=tk.LEFT)
        entry = ttk.Entry(frame)
        entry.pack(side=tk.LEFT)
        return entry

    def _save_coords(self, dialog, address, lat_entry, lng_entry, result):
        try:
            lat = float(lat_entry.get())
            lng = float(lng_entry.get())
            self.geocoding_handler.cache[address] = (lat, lng)
            self.geocoding_handler.save_cache()
            result.extend([lat, lng])
            dialog.destroy()
        except ValueError:
            messagebox.showerror("Ошибка", "Введите числовые значения координат")

    def log_error(self, message):
        self.status_bar.config(text=message)
        self.status_bar.update_idletasks()