"""
Модуль алгоритма распределения адресов
Содержит логику автоматического распределения адресов по группам
"""

import math
from typing import List, Dict, Tuple, Set, Optional, Any


class DistributionAlgorithm:
    """Класс для автоматического распределения адресов по группам"""
    
    def __init__(self, minsk_center: Tuple[float, float] = (53.9045, 27.5615)):
        self.minsk_center = minsk_center
        self.distribution_params = None
        self.current_session_colors = set()
        
    def initialize_distribution(self, available_addresses: List[Dict], 
                              max_radius: float, sector_width: float, 
                              group_size: int, base_angle: float = 0) -> Dict:
        """Инициализирует параметры распределения"""
        
        # Инициализируем список цветов текущей сессии распределения ТОЛЬКО если его нет
        if not hasattr(self, 'current_session_colors'):
            self.current_session_colors = set()
            print(f"🎨 Инициализирован новый список цветов текущей сессии распределения")
        else:
            print(f"🎨 Продолжаем с существующими цветами сессии: {sorted(self.current_session_colors)}")
        
        # Сохраняем параметры для продолжения распределения
        self.distribution_params = {
            'available_addresses': available_addresses,
            'center': self.minsk_center,
            'base_angle': base_angle,
            'max_radius': max_radius,
            'sector_width': sector_width,
            'group_size': group_size,
            'current_angle': base_angle,
            'remainder_from_previous': [],
            'last_cluster_center': None,  # Для отслеживания центра предыдущего кластера
            'manually_processed_addresses': set()  # Отслеживаем адреса, обработанные вручную
        }
        
        print(f"🎨 Инициализированы цвета текущей сессии: {sorted(self.current_session_colors)}")
        return self.distribution_params
    
    def get_next_group(self) -> List[Dict]:
        """Получает следующую группу адресов для распределения"""
        if not self.distribution_params:
            return []
            
        params = self.distribution_params
        
        # Проверяем, есть ли остатки от предыдущего распределения
        if params['remainder_from_previous']:
            print(f"📦 Используем остатки от предыдущего распределения: {len(params['remainder_from_previous'])} адресов")
            group_to_process = params['remainder_from_previous'][:params['group_size']]
            params['remainder_from_previous'] = params['remainder_from_previous'][params['group_size']:]
            
            if group_to_process:
                return group_to_process
        
        # Если остатков нет или их недостаточно, ищем новую группу
        current_group = []
        
        # Ищем адреса в секторах, пока не наберем нужное количество
        max_iterations = 20  # Защита от бесконечного цикла
        iteration = 0
        
        while len(current_group) < params['group_size'] and params['available_addresses'] and iteration < max_iterations:
            iteration += 1
            
            # Определяем границы текущего сектора
            start_angle = params['current_angle']
            end_angle = (params['current_angle'] - params['sector_width']) % 360
            
            print(f"Сектор {iteration}: {start_angle:.2f}° - {end_angle:.2f}°")
            
            # Находим адреса в текущем секторе с адаптивным радиусом
            sector_addresses = []
            addresses_to_remove = []
            
            # Начинаем с базового радиуса и увеличиваем при необходимости
            current_radius = params['max_radius']
            max_search_radius = params['max_radius'] * 2.5  # Максимальный радиус поиска
            
            while current_radius <= max_search_radius:
                sector_addresses = []
                addresses_to_remove = []
                
                for addr_info in params['available_addresses']:
                    if self.point_in_sector(
                        addr_info['lat'], addr_info['lng'],
                        params['center'][0], params['center'][1],
                        start_angle, end_angle, current_radius
                    ):
                        sector_addresses.append(addr_info)
                        addresses_to_remove.append(addr_info)
                
                # Если нашли достаточно адресов, прерываем поиск
                if len(sector_addresses) >= 3:  # Минимум 3 адреса в секторе
                    break
                    
                # Увеличиваем радиус поиска
                current_radius += params['max_radius'] * 0.5
                print(f"  Увеличиваем радиус поиска до {current_radius:.1f} км")
            
            print(f"  Найдено в секторе: {len(sector_addresses)} адресов (радиус: {current_radius:.1f} км)")
            
            if sector_addresses:
                # Добавляем найденные адреса в текущую группу
                space_left = params['group_size'] - len(current_group)
                addresses_to_add = sector_addresses[:space_left]
                current_group.extend(addresses_to_add)
                
                # Удаляем использованные адреса из доступных
                for addr_info in addresses_to_add:
                    if addr_info in params['available_addresses']:
                        params['available_addresses'].remove(addr_info)
                
                print(f"  Добавлено в группу: {len(addresses_to_add)} адресов")
                print(f"  Размер группы: {len(current_group)}/{params['group_size']}")
                
                # Если группа заполнена, сохраняем остатки
                if len(current_group) >= params['group_size']:
                    remaining_from_sector = sector_addresses[space_left:]
                    if remaining_from_sector:
                        params['remainder_from_previous'].extend(remaining_from_sector)
                        # Удаляем остатки из доступных адресов
                        for addr_info in remaining_from_sector:
                            if addr_info in params['available_addresses']:
                                params['available_addresses'].remove(addr_info)
                        print(f"  Сохранено остатков для следующей группы: {len(remaining_from_sector)} адресов")
                    break
            
            # Переходим к следующему сектору
            params['current_angle'] = end_angle
            
            # Если прошли полный круг, увеличиваем радиус
            if abs(params['current_angle'] - params['base_angle']) < params['sector_width']:
                params['max_radius'] += 5  # Увеличиваем радиус на 5 км
                print(f"🔄 Полный круг завершен, увеличиваем радиус до {params['max_radius']} км")
        
        # Обработка случая, когда кластер содержит менее 8 адресов
        if len(current_group) < 8 and params['available_addresses']:
            print(f"⚠️ Кластер содержит только {len(current_group)} адресов (< 8)")
            
            # Находим ближайший адрес к центру предыдущего неполного кластера
            if current_group:
                # Вычисляем центр текущего кластера
                cluster_center_lat = sum(addr['lat'] for addr in current_group) / len(current_group)
                cluster_center_lng = sum(addr['lng'] for addr in current_group) / len(current_group)
                params['last_cluster_center'] = (cluster_center_lat, cluster_center_lng)
                
                print(f"💾 Сохранен центр кластера для следующего распределения: {cluster_center_lat:.6f}, {cluster_center_lng:.6f}")
        
        return current_group
    
    def update_available_addresses(self, all_markers: Dict, selected_markers: Dict, 
                                 selected_markers2: Dict) -> None:
        """Обновляет список доступных адресов для распределения"""
        if not self.distribution_params:
            return
        
        print("🔄 Обновляем список доступных адресов после ручных изменений...")
        
        # Получаем текущий список всех адресов, которые не в списках 1 и 2
        current_available = []
        manually_processed = self.distribution_params.get('manually_processed_addresses', set())
        
        for address, marker in all_markers.items():
            # Пропускаем адреса, которые уже в списках 1 или 2
            if address not in selected_markers and address not in selected_markers2:
                # Пропускаем адреса, которые были обработаны вручную
                if address in manually_processed:
                    print(f"  🔧 Пропускаем вручную обработанный адрес: {address}")
                    continue
                    
                try:
                    lat, lng = marker.position
                    current_available.append({
                        'address': address,
                        'lat': lat,
                        'lng': lng,
                        'marker': marker
                    })
                    print(f"  ✅ Доступен: {address}")
                except Exception as e:
                    print(f"  ❌ Ошибка с маркером для {address}: {e}")
        
        # Обновляем список в параметрах распределения
        old_count = len(self.distribution_params['available_addresses'])
        self.distribution_params['available_addresses'] = current_available
        new_count = len(current_available)
        
        print(f"📊 Было доступных адресов: {old_count}")
        print(f"📊 Стало доступных адресов: {new_count}")
    
    def mark_manually_processed(self, addresses: List[str]) -> None:
        """Отмечает адреса как обработанные вручную"""
        if not self.distribution_params:
            return
        
        for address in addresses:
            self.distribution_params['manually_processed_addresses'].add(address)
            print(f"🔧 Адрес '{address}' отмечен как обработанный вручную")
    
    def get_manually_removed_addresses(self, all_markers: Dict, selected_markers: Dict, 
                                     selected_markers2: Dict) -> List[str]:
        """Возвращает список вручную убранных адресов"""
        if not self.distribution_params:
            return []
        
        manually_processed = self.distribution_params.get('manually_processed_addresses', set())
        
        # Собираем все адреса, которые сейчас доступны (не в списках 1 и 2)
        available_in_main_list = set()
        for address, marker in all_markers.items():
            if address not in selected_markers and address not in selected_markers2:
                available_in_main_list.add(address)
        
        # Находим пересечение - адреса, которые были обработаны вручную И сейчас доступны
        manually_removed = []
        for address in manually_processed:
            if address in available_in_main_list:
                manually_removed.append(address)
        
        return manually_removed
    
    def restore_addresses_to_distribution(self, addresses: List[str]) -> None:
        """Возвращает адреса в распределение"""
        if not self.distribution_params:
            return
        
        for address in addresses:
            if address in self.distribution_params['manually_processed_addresses']:
                self.distribution_params['manually_processed_addresses'].remove(address)
                print(f"✅ Адрес '{address}' возвращен в распределение")
    
    def add_session_color(self, color: str) -> None:
        """Добавляет цвет в текущую сессию"""
        if not hasattr(self, 'current_session_colors'):
            self.current_session_colors = set()
        self.current_session_colors.add(color)
        print(f"✅ Цвет {color} добавлен в current_session_colors")
    
    def get_session_colors(self) -> Set[str]:
        """Возвращает цвета текущей сессии"""
        if not hasattr(self, 'current_session_colors'):
            self.current_session_colors = set()
        return self.current_session_colors
    
    def clear_session_colors(self) -> None:
        """Очищает цвета сессии"""
        if hasattr(self, 'current_session_colors'):
            print(f"🎨 Сбрасываем цвета сессии: {sorted(self.current_session_colors)}")
            self.current_session_colors = set()
    
    def has_remaining_addresses(self) -> bool:
        """Проверяет, есть ли еще адреса для распределения"""
        if not self.distribution_params:
            return False
        
        return (len(self.distribution_params['available_addresses']) + 
                len(self.distribution_params['remainder_from_previous'])) > 0
    
    def calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Вычисляет расстояние между двумя точками в километрах (формула гаверсинуса)"""
        # Радиус Земли в километрах
        R = 6371.0
        
        # Преобразуем градусы в радианы
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Разности координат
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        # Формула гаверсинуса
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Вычисляет азимут (направление) от точки 1 к точке 2 в градусах"""
        # Преобразуем градусы в радианы
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Разность долгот
        dlon = lon2_rad - lon1_rad
        
        # Вычисляем азимут
        y = math.sin(dlon) * math.cos(lat2_rad)
        x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon)
        
        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)
        
        # Нормализуем к диапазону 0-360
        return (bearing_deg + 360) % 360
    
    def point_in_sector(self, point_lat: float, point_lon: float, center_lat: float, center_lon: float,
                       start_angle: float, end_angle: float, max_distance: float) -> bool:
        """Проверяет, находится ли точка в заданном секторе"""
        # Вычисляем расстояние от центра до точки
        distance = self.calculate_distance(center_lat, center_lon, point_lat, point_lon)
        
        # Проверяем расстояние
        if distance > max_distance:
            return False
        
        # Вычисляем угол от центра к точке
        bearing = self.calculate_bearing(center_lat, center_lon, point_lat, point_lon)
        
        # Нормализуем углы
        start_angle = start_angle % 360
        end_angle = end_angle % 360
        
        # Проверяем, находится ли угол в секторе
        if start_angle <= end_angle:
            return start_angle <= bearing <= end_angle
        else:
            # Сектор пересекает 0 градусов
            return bearing >= start_angle or bearing <= end_angle
