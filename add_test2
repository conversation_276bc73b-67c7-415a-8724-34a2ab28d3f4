import pandas as pd
import googlemaps
import time
import os
from tqdm import tqdm

def geocode_addresses(input_file, api_key):
    """
    Обрабатывает Excel-файл с адресами, добавляет координаты через Google Maps API
    и сохраняет необработанные адреса в отдельный файл.
    Координаты сохраняются с точкой в качестве разделителя.
    """
    # Загрузка данных из Excel
    try:
        df = pd.read_excel(input_file)
    except Exception as e:
        print(f"Ошибка при чтении файла: {e}")
        return
    
    # Проверка структуры файла
    if len(df.columns) < 1:
        print("Файл должен содержать хотя бы один столбец с адресами")
        return
    
    address_col = df.columns[0]  # Первый столбец с адресами
    
    # Добавляем колонки для координат, если их нет
    if 'Широта' not in df.columns:
        df['Широта'] = None
    if 'Долгота' not in df.columns:
        df['Долгота'] = None
    
    # Инициализация Google Maps клиента
    gmaps = googlemaps.Client(key=api_key)
    
    # Списки для результатов
    success_addresses = []
    failed_addresses = []
    
    # Обработка адресов с прогресс-баром
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Обработка адресов"):
        address = row[address_col]
        
        if pd.isna(address) or str(address).strip() == '':
            continue
        
        try:
            # Геокодирование адреса
            geocode_result = gmaps.geocode(address)
            
            if geocode_result:
                location = geocode_result[0]['geometry']['location']
                # Сохраняем координаты как числа с точкой
                df.at[index, 'Широта'] = float(location['lat'])
                df.at[index, 'Долгота'] = float(location['lng'])
                success_addresses.append(address)
            else:
                failed_addresses.append(address)
            
            # Ограничение API (50 запросов в секунду)
            time.sleep(0.02)
            
        except Exception as e:
            print(f"Ошибка при обработке адреса '{address}': {e}")
            failed_addresses.append(address)
    
    # Сохранение результатов с гарантированным использованием точки
    output_file = os.path.splitext(input_file)[0] + "_with_coordinates.xlsx"
    
    # Создаем Excel writer с явным указанием формата чисел
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)
        # Принудительно устанавливаем числовой формат для координат
        workbook = writer.book
        worksheet = writer.sheets['Sheet1']
        number_format = '0.000000'  # Формат с точкой
        
        # Находим индексы колонок с координатами
        lat_col_idx = df.columns.get_loc('Широта') + 1  # +1 т.к. Excel считает с 1
        lng_col_idx = df.columns.get_loc('Долгота') + 1
        
        # Применяем формат ко всем ячейкам в колонках
        for row in range(2, len(df)+2):  # Строки в Excel начинаются с 1 + заголовок
            worksheet.cell(row=row, column=lat_col_idx).number_format = number_format
            worksheet.cell(row=row, column=lng_col_idx).number_format = number_format
    
    print(f"\nРезультаты сохранены в: {output_file}")
    
    # Сохранение необработанных адресов
    if failed_addresses:
        failed_df = pd.DataFrame({address_col: failed_addresses})
        failed_file = os.path.splitext(input_file)[0] + "_failed_addresses.xlsx"
        failed_df.to_excel(failed_file, index=False)
        print(f"Необработанные адресы сохранены в: {failed_file}")
        print(f"Количество необработанных адресов: {len(failed_addresses)}")
    
    print(f"Успешно обработано адресов: {len(success_addresses)}")

if __name__ == "__main__":
    # Пример использования
    input_excel = "test_file.xlsx"  # Путь к вашему файлу
    api_key = "AIzaSyBikxnCmNgmoerGPe0UOk5NukKfyd7EmC0"  # Ваш ключ API
    
    geocode_addresses(input_excel, api_key)