import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkintermapview import TkinterMapView
import pandas as pd
import os
import googlemaps
import time
from datetime import datetime
import requests 

class YandexGeocoder:
    """Класс для работы с Яндекс Геокодером"""
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://geocode-maps.yandex.ru/1.x/"

    def geocode(self, address):
        """Геокодирование адреса через Яндекс API"""
        params = {
            "apikey": self.api_key,
            "geocode": address,
            "format": "json",
            "lang": "ru_RU"
        }
        try:
            response = requests.get(self.base_url, params=params).json()
            features = response["response"]["GeoObjectCollection"]["featureMember"]
            if not features:
                return None, None
                
            # Берем первый результат
            pos = features[0]["GeoObject"]["Point"]["pos"]
            lon, lat = map(float, pos.split())
            return lat, lon
        except Exception as e:
            print(f"Ошибка Яндекс Геокодера: {e}")
            return None, None

    def suggest_similar(self, query):
        """Поиск похожих адресов через API Геоседжеста"""
        url = "https://suggest-maps.yandex.ru/v1/suggest"
        params = {
            "apikey": self.api_key,
            "text": query,
            "type": "geo",
            "lang": "ru_RU"
        }
        try:
            response = requests.get(url, params=params).json()
            return [item["display_name"] for item in response.get("results", [])[:5]]  # первые 5 вариантов
        except Exception as e:
            print(f"Ошибка подсказок Яндекс: {e}")
            return []

class MapApp:
    def __init__(self, root):

        self.root = root
        self.root.title("Карта с загрузкой адресов")
        self.root.geometry("1400x800")
        
        # Закрытый API ключ (замените на ваш реальный ключ)
        self.GOOGLE_MAPS_API_KEY = "AIzaSyBikxnCmNgmoerGPe0UOk5NukKfyd7EmC0"
        self.YANDEX_MAPS_API_KEY = "e1bf17bc-d8ae-42af-a034-643d092fe2ce"  # Добавьте это
        
        # Основной контейнер с возможностью изменения размеров
        self.main_paned = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)
        
        # Левый фрейм (управление)
        self.left_frame = ttk.Frame(self.main_paned, width=300)
        self.main_paned.add(self.left_frame)
        
        # Правый контейнер с вертикальным разделением
        self.right_paned = ttk.PanedWindow(self.main_paned, orient=tk.VERTICAL)
        self.main_paned.add(self.right_paned)
        
        # Верхний правый фрейм (карта и управление)
        self.top_right_frame = ttk.Frame(self.right_paned)
        self.right_paned.add(self.top_right_frame, weight=1)
        
        # Нижний правый фрейм (списки адресов)
        self.bottom_right_frame = ttk.Frame(self.right_paned)
        self.right_paned.add(self.bottom_right_frame, weight=1)
        
        # Карта
        self.map_widget = TkinterMapView(self.top_right_frame, width=700, height=400)
        self.map_widget.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.map_widget.set_position(55.7558, 37.6176)  # Москва по умолчанию
        self.map_widget.set_zoom(12)
        
        # Панель загрузки файлов (слева от карты)
        self.file_frame = ttk.Frame(self.top_right_frame, width=200)
        self.file_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=5, pady=5)
        
        # Контейнер для списков адресов
        self.lists_paned = ttk.PanedWindow(self.bottom_right_frame, orient=tk.HORIZONTAL)
        self.lists_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.selected_lists_paned = ttk.PanedWindow(self.lists_paned, orient=tk.VERTICAL)
        self.lists_paned.add(self.selected_lists_paned, weight=1)
        
        # Фрейм для всех адресов с фильтрацией
        self.all_addresses_container = ttk.Frame(self.lists_paned)
        self.lists_paned.add(self.all_addresses_container, weight=2)
        
        # Поле фильтрации
        self.filter_frame = ttk.Frame(self.all_addresses_container)
        self.filter_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(self.filter_frame, text="Фильтр:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar()
        self.filter_entry = ttk.Entry(self.filter_frame, textvariable=self.filter_var)
        self.filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.filter_var.trace("w", lambda *args: self.apply_filter())
        
        ttk.Button(
            self.filter_frame, 
            text="×", 
            width=3,
            command=self.reset_filter
        ).pack(side=tk.LEFT, padx=5)
        
        # Основной список адресов
        self.all_addresses_frame = ttk.LabelFrame(self.all_addresses_container, text="Все адреса")
        self.all_addresses_frame.pack(fill=tk.BOTH, expand=True)
        
        self.all_addresses_list = tk.Listbox(self.all_addresses_frame)
        self.all_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.all_addresses_scroll = ttk.Scrollbar(self.all_addresses_list)
        self.all_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.all_addresses_list.config(yscrollcommand=self.all_addresses_scroll.set)
        self.all_addresses_scroll.config(command=self.all_addresses_list.yview)
        
        # Список избранных адресов
        self.selected_addresses_frame = ttk.LabelFrame(self.selected_lists_paned, text="Список 1 (0)")
        self.selected_lists_paned.add(self.selected_addresses_frame, weight=1)
        
        # Выбор водителя
        self.selector_frame = ttk.Frame(self.selected_lists_paned)
        self.selected_lists_paned.add(self.selector_frame)

        self.list_selector = tk.StringVar(value="list1")
        ttk.Radiobutton(
            self.selector_frame,
            text="Список 1",
            variable=self.list_selector,
            value="list1"
        ).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(
            self.selector_frame,
            text="Список 2",
            variable=self.list_selector,
            value="list2"
        ).pack(side=tk.LEFT, padx=5)

        self.driver_frame = ttk.Frame(self.selected_addresses_frame)
        self.driver_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(self.driver_frame, text="Водитель:").pack(side=tk.LEFT)
        self.driver_var = tk.StringVar()
        self.driver_combobox = ttk.Combobox(
            self.driver_frame, 
            textvariable=self.driver_var,
            values=["Иванов", "Петров", "Сидоров", "Смирнов"],
            state="normal"
        )
        self.driver_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.selected_addresses_list = tk.Listbox(self.selected_addresses_frame)
        self.selected_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.selected_addresses_scroll = ttk.Scrollbar(self.selected_addresses_list)
        self.selected_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.selected_addresses_list.config(yscrollcommand=self.selected_addresses_scroll.set)
        self.selected_addresses_scroll.config(command=self.selected_addresses_list.yview)
        
        # Вставляем после всего кода первого списка (примерно строка 150):
        self.selected_addresses_frame2 = ttk.LabelFrame(self.selected_lists_paned, text="Список 2 (0)")
        self.selected_lists_paned.add(self.selected_addresses_frame2, weight=1)

        self.selected_addresses_list2 = tk.Listbox(self.selected_addresses_frame2)
        self.selected_addresses_list2.pack(fill=tk.BOTH, expand=True)
        self.selected_addresses_scroll2 = ttk.Scrollbar(self.selected_addresses_list2)
        self.selected_addresses_scroll2.pack(side=tk.RIGHT, fill=tk.Y)
        self.selected_addresses_list2.config(yscrollcommand=self.selected_addresses_scroll2.set)
        self.selected_addresses_scroll2.config(command=self.selected_addresses_list2.yview)
        self.selected_addresses_list2.bind("<Double-Button-1>", self.return_to_all_from_list2)

        # Список необработанных адресов
        self.failed_addresses_frame = ttk.LabelFrame(self.left_frame, text="Необработанные адреса")
        self.failed_addresses_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.failed_addresses_btn_frame = ttk.Frame(self.failed_addresses_frame)
        self.failed_addresses_btn_frame.pack(fill=tk.X)
        
        ttk.Button(
            self.failed_addresses_btn_frame,
            text="Удалить выделенное",
            command=self.delete_selected_failed
        ).pack(side=tk.LEFT, fill=tk.X, expand=True)


        # Список удаленных адресов
        self.deleted_addresses_frame = ttk.LabelFrame(self.left_frame, text="Удаленные адреса")
        self.deleted_addresses_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.deleted_addresses_list = tk.Listbox(self.deleted_addresses_frame)
        self.deleted_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.deleted_addresses_scroll = ttk.Scrollbar(self.deleted_addresses_list)
        self.deleted_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.deleted_addresses_list.config(yscrollcommand=self.deleted_addresses_scroll.set)
        self.deleted_addresses_scroll.config(command=self.deleted_addresses_list.yview)
        
        # Привязываем двойной клик для восстановления
        self.deleted_addresses_list.bind("<Double-Button-1>", self.restore_deleted_address)
        
        # Кнопка для очистки списка удаленных
        ttk.Button(
            self.deleted_addresses_frame,
            text="Очистить список",
            command=self.clear_deleted_list
        ).pack(fill=tk.X, pady=5)
        
        self.failed_addresses_list = tk.Listbox(self.failed_addresses_frame)
        self.failed_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.failed_addresses_scroll = ttk.Scrollbar(self.failed_addresses_list)
        self.failed_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.failed_addresses_list.config(yscrollcommand=self.failed_addresses_scroll.set)
        self.failed_addresses_scroll.config(command=self.failed_addresses_list.yview)

        
        # Добавляем обработчики горячих клавиш
        self.root.bind('<Control-c>', self.copy_selected)
        self.root.bind('<Control-v>', self.paste_to_entry)
        
        # Создаем контекстное меню
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Копировать", command=self.copy_selected)
        self.context_menu.add_command(label="Вставить", command=self.paste_to_entry)
        

        
        # Буфер обмена
        self.clipboard = ""
        
        # Панель ручного ввода адреса и координат
        self.manual_frame = ttk.LabelFrame(self.left_frame, text="Ручной ввод данных")
        self.manual_frame.pack(fill=tk.X, pady=5)
        
        # Поле для адреса
        ttk.Label(self.manual_frame, text="Адрес:").pack(pady=(5, 0))
        self.address_entry = ttk.Entry(self.manual_frame)
        self.address_entry.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Поля для координат
        coord_frame = ttk.Frame(self.manual_frame)
        coord_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(coord_frame, text="Широта:").pack(side=tk.LEFT)
        self.lat_entry = ttk.Entry(coord_frame, width=12)
        self.lat_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(coord_frame, text="Долгота:").pack(side=tk.LEFT)
        self.lng_entry = ttk.Entry(coord_frame, width=12)
        self.lng_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # Кнопка добавления
        ttk.Button(
            self.manual_frame,
            text="Добавить вручную",
            command=self.add_manual
        ).pack(pady=5, fill=tk.X)

        # Кнопка добавления только адреса
        ttk.Button(
            self.left_frame,
            text="Вставить только адрес",
            command=lambda: self.address_entry.insert(tk.END, self.root.clipboard_get())
        ).pack(pady=2, fill=tk.X)
        
        # Настройка элементов управления
        self.setup_left_panel()
        
        # Хранилище данных
        self.markers = {}
        self.selected_markers = {}
        self.selected_markers2 = {}
        self.original_markers = {}
        self.failed_addresses = []
        self.deleted_addresses = []  # Новый список для хранения удаленных адресов
        
        # Инициализация геокодера
        # self.init_geocoder()
        self.init_geocoders()
        
        # Привязка событий
        self.all_addresses_list.bind("<Double-Button-1>", self.add_to_selected)
        self.selected_addresses_list.bind("<Double-Button-1>", self.return_to_all)
        # Привязываем контекстное меню к спискам и полю ввода
        self.all_addresses_list.bind("<Button-3>", self.show_context_menu)
        self.selected_addresses_list.bind("<Button-3>", self.show_context_menu)
        self.failed_addresses_list.bind("<Button-3>", self.show_context_menu)
        self.address_entry.bind("<Button-3>", self.show_context_menu)

        # Контекстное меню для списка удаленных
        self.deleted_menu = tk.Menu(self.root, tearoff=0)
        self.deleted_menu.add_command(label="Восстановить", command=self.restore_deleted_address)
        self.deleted_menu.add_command(label="Копировать", command=lambda: self.copy_from_list(self.deleted_addresses_list))
        
        self.deleted_addresses_list.bind("<Button-3>", lambda e: self.show_deleted_context_menu(e))

    def add_to_selected(self, event=None):
        """Добавление адреса в выбранный список"""
        selection = self.all_addresses_list.curselection()
        if not selection:
            return
        
        address = self.all_addresses_list.get(selection[0])
        
        # Определяем целевой список и цвет маркера
        if self.list_selector.get() == "list1":
            target_markers = self.selected_markers
            target_list = self.selected_addresses_list
            marker_color = "blue"
        else:
            target_markers = self.selected_markers2
            target_list = self.selected_addresses_list2
            marker_color = "green"
        
        # Проверяем, нет ли уже этого адреса в целевом списке
        if address in target_markers:
            return
        
        # Удаляем из основного списка
        self.all_addresses_list.delete(selection[0])
        
        # Удаляем старый маркер (если есть)
        if address in self.markers:
            self.markers[address].delete()
        
        # Получаем координаты (из original_markers или geocode)
        if address in self.original_markers:
            lat, lng = self.original_markers[address].position
        else:
            # Если нет в original_markers, пробуем геокодировать
            lat, lng = self.geocode_address(address)
            if lat is None or lng is None:
                self.failed_addresses.append(address)
                self.failed_addresses_list.insert(tk.END, address)
                return
        
        # Создаем новый маркер с нужным цветом
        new_marker = self.map_widget.set_marker(
            lat, lng,
            text=address,
            marker_color_circle=marker_color,
            marker_color_outside=marker_color,
            text_color=marker_color
        )
        
        # Добавляем в соответствующие хранилища
        target_markers[address] = new_marker
        self.markers[address] = new_marker
        target_list.insert(tk.END, address)
        
        # Обновляем счетчики
        self.update_counts()

    def return_to_all(self, event=None):
        """Возвращение адреса из избранного в основной список"""
        selection = self.selected_addresses_list.curselection()
        if not selection:
            return
        
        address = self.selected_addresses_list.get(selection[0])
        
        if address not in self.selected_markers:
            return
        
        # Удаляем из избранного
        self.selected_addresses_list.delete(selection[0])
        
        # Получаем маркер
        marker = self.selected_markers[address]
        
        # Удаляем старый маркер
        if marker in self.map_widget.canvas_marker_list:
            marker.delete()
        
        # Создаем новый обычный маркер
        new_marker = self.map_widget.set_marker(
            marker.position[0], 
            marker.position[1], 
            text=address
        )
        
        # Обновляем хранилища
        self.markers[address] = new_marker
        self.original_markers[address] = new_marker
        del self.selected_markers[address]
        
        # Добавляем в основной список
        current_filter = self.filter_var.get().lower()
        if not current_filter or current_filter in address.lower():
            if address not in self.all_addresses_list.get(0, tk.END):
                self.all_addresses_list.insert(tk.END, address)
        
        self.update_counts()

    def return_to_all_from_list2(self, event=None):
        """Возврат адреса из второго списка в основной с полным удалением маркера"""
        selection = self.selected_addresses_list2.curselection()
        if not selection:
            return
        
        address = self.selected_addresses_list2.get(selection[0])
        
        if address not in self.selected_markers2:
            return
        
        # Получаем маркер перед удалением
        marker = self.selected_markers2[address]
        
        # Удаляем из всех хранилищ
        self.selected_addresses_list2.delete(selection[0])
        del self.selected_markers2[address]
        
        # Полностью удаляем маркер с карты
        if marker in self.map_widget.canvas_marker_list:
            marker.delete()
        
        # Удаляем из основного словаря маркеров
        if address in self.markers:
            del self.markers[address]
        
        # Но сохраняем в original_markers для возможного восстановления
        if address in self.original_markers:
            # Создаем новый обычный маркер
            new_marker = self.map_widget.set_marker(
                marker.position[0], 
                marker.position[1], 
                text=address
            )
            self.original_markers[address] = new_marker
            self.markers[address] = new_marker
        
        # Добавляем в основной список, если соответствует фильтру
        current_filter = self.filter_var.get().lower()
        if not current_filter or current_filter in address.lower():
            if address not in self.all_addresses_list.get(0, tk.END):
                self.all_addresses_list.insert(tk.END, address)
        
        self.update_counts()

    def update_counts(self):
        """Обновление всех счетчиков"""
        self.update_address_count()
        self.selected_addresses_frame.config(text=f"Список 1 ({len(self.selected_markers)})")
        self.selected_addresses_frame2.config(text=f"Список 2 ({len(self.selected_markers2)})")
    
    
    def init_geocoders(self):
        """Инициализация всех геокодеров"""
        # Google Maps
        try:
            self.gmaps_client = googlemaps.Client(key=self.GOOGLE_MAPS_API_KEY)
            test_result = self.gmaps_client.geocode('Red Square, Moscow', region='ru')
            if not test_result:
                print("Google API работает, но не возвращает результаты")
        except Exception as e:
            print(f"Ошибка Google API: {e}")
            self.gmaps_client = None
    
        # Яндекс Геокодер
        self.yandex_geocoder = YandexGeocoder(self.YANDEX_MAPS_API_KEY)
    
    
    def show_deleted_context_menu(self, event):
        """Показывает контекстное меню для списка удаленных"""
        try:
            self.deleted_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.deleted_menu.grab_release()

    def copy_from_list(self, list_widget):
        """Копирует выделенный адрес из указанного списка"""
        selection = list_widget.curselection()
        if selection:
            address = list_widget.get(selection[0])
            self.root.clipboard_clear()
            self.root.clipboard_append(address)

    def add_to_deleted(self, address):
        """Добавляет адрес в список удаленных"""
        if address and address not in self.deleted_addresses:
            self.deleted_addresses.append(address)
            self.deleted_addresses_list.insert(tk.END, address)

    def restore_deleted_address(self, event=None):
        """Восстанавливает адрес из удаленных"""
        selection = self.deleted_addresses_list.curselection()
        if not selection:
            return
        
        address = self.deleted_addresses_list.get(selection[0])
        
        # Удаляем из списка удаленных
        self.deleted_addresses.remove(address)  # Удаляем из внутреннего списка
        self.deleted_addresses_list.delete(selection[0])  # Удаляем из виджета
        
        # Вставляем в поле ручного ввода
        self.address_entry.delete(0, tk.END)
        self.address_entry.insert(0, address)

    def clear_deleted_list(self):
        """Очищает список удаленных"""
        if not self.deleted_addresses:
            return
        
        if messagebox.askyesno("Подтверждение", "Очистить весь список удаленных адресов?"):
            self.deleted_addresses = []  # Очищаем внутренний список
            self.deleted_addresses_list.delete(0, tk.END)  # Очищаем виджет

    def update_selected_count(self):
        """Обновляет отображение количества избранных адресов"""
        count = len(self.selected_markers)
        self.selected_addresses_frame.config(text=f"Избранные адреса ({count})")

    def copy_selected(self, event=None):
        """Копирование выделенного текста в разных форматах"""
        widget = self.root.focus_get()
        
        if widget in [self.all_addresses_list, self.selected_addresses_list, self.failed_addresses_list]:
            selection = widget.curselection()
            if selection:
                address = widget.get(selection[0])
                if address in self.markers:
                    marker = self.markers[address]
                    # Формат 1: координаты, адрес (как в примере)
                    self.clipboard = f"{marker.position[0]}, {marker.position[1]}\n{address}"
                    # Формат 2: для вставки в поля (адрес[tab]широта[tab]долгота)
                    self.clipboard_tsv = f"{address}\t{marker.position[0]}\t{marker.position[1]}"
                else:
                    self.clipboard = address
                    self.clipboard_tsv = address
                self.root.clipboard_clear()
                self.root.clipboard_append(self.clipboard)

    def paste_to_entry(self, event=None):
        """Вставка данных из буфера с улучшенным определением формата"""
        try:
            clipboard_text = self.root.clipboard_get().strip()
            
            # Очищаем все поля перед вставкой
            self.address_entry.delete(0, tk.END)
            self.lat_entry.delete(0, tk.END)
            self.lng_entry.delete(0, tk.END)
            
            # Разбиваем текст на строки
            lines = clipboard_text.split('\n')
            
            # Случай 1: Две строки - координаты и адрес (как при копировании из списка)
            if len(lines) == 2:
                first_line = lines[0].strip()
                second_line = lines[1].strip()
                
                # Проверяем, является ли первая строка координатами
                if ',' in first_line and all(self.is_valid_coordinate(x.strip()) for x in first_line.split(',')[:2]):
                    coords = first_line.split(',')
                    self.lat_entry.insert(0, coords[0].strip())
                    self.lng_entry.insert(0, coords[1].strip())
                    self.address_entry.insert(0, second_line)
                else:
                    # Если первая строка не координаты, считаем весь текст адресом
                    self.address_entry.insert(0, clipboard_text.replace('\n', ' '))
            
            # Случай 2: Одна строка с табуляцией (адрес\tширота\tдолгота)
            elif '\t' in clipboard_text:
                parts = clipboard_text.split('\t')
                if len(parts) >= 3:
                    self.address_entry.insert(0, parts[0].strip())
                    self.lat_entry.insert(0, parts[1].strip())
                    self.lng_entry.insert(0, parts[2].strip())
                else:
                    self.address_entry.insert(0, clipboard_text.replace('\t', ' '))
            
            # Случай 3: Одна строка с запятой (возможно координаты)
            elif ',' in clipboard_text:
                parts = [x.strip() for x in clipboard_text.split(',')]
                # Проверяем, что это координаты (два числа)
                if len(parts) >= 2 and all(self.is_valid_coordinate(x) for x in parts[:2]):
                    self.lat_entry.insert(0, parts[0])
                    self.lng_entry.insert(0, parts[1])
                else:
                    # Если не координаты, то это адрес с запятыми
                    self.address_entry.insert(0, clipboard_text)
            
            # Случай 4: Просто текст (адрес)
            else:
                self.address_entry.insert(0, clipboard_text)
                
        except tk.TclError:
            pass  # Буфер обмена пуст

    def is_valid_coordinate(self, value):
        """Проверяет, является ли значение корректной координатой"""
        try:
            num = float(value)
            return True
        except ValueError:
            return False

    def show_context_menu(self, event):
        """Показ контекстного меню"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def load_additional_excel(self):
        """Загрузка дополнительных адресов из Excel файла"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            # Читаем Excel файл
            df = pd.read_excel(file_path)
            
            if len(df.columns) < 1:
                messagebox.showerror("Ошибка", "Файл должен содержать хотя бы один столбец с адресами")
                return
            
            # Определяем столбец с адресами
            address_col = df.columns[0]
            
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Обработка адресов")
            progress_label = ttk.Label(progress_window, text="Добавление адресов...")
            progress_label.pack(pady=10)
            progress = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress.pack(pady=10)
            progress_window.update()
            
            total = len(df)
            processed = 0
            success_count = 0
            new_failed = 0
            
            for _, row in df.iterrows():
                address = str(row[address_col]) if pd.notna(row[address_col]) else ""
                
                if address and address.strip() and address not in self.original_markers:
                    lat, lng = None, None
                    
                    # Пробуем получить координаты из файла (если есть)
                    if len(df.columns) >= 3:
                        try:
                            lat_col = df.columns[1]
                            lng_col = df.columns[2]
                            lat = float(row[lat_col]) if pd.notna(row[lat_col]) else None
                            lng = float(row[lng_col]) if pd.notna(row[lng_col]) else None
                        except (ValueError, TypeError):
                            pass
                    
                    # Если координат нет в файле или они некорректны, геокодируем
                    if lat is None or lng is None or not self.validate_coordinates(lat, lng):
                        lat, lng = self.geocode_address(address)
                    
                    if lat is not None and lng is not None:
                        marker = self.map_widget.set_marker(lat, lng, text=address)
                        self.markers[address] = marker
                        self.original_markers[address] = marker
                        if address not in self.selected_markers:
                            self.all_addresses_list.insert(tk.END, address)
                        success_count += 1
                    else:
                        self.failed_addresses.append(address)
                        self.failed_addresses_list.insert(tk.END, address)
                        new_failed += 1
                
                processed += 1
                progress['value'] = (processed / total) * 100
                progress_label.config(text=f"Обработано: {processed}/{total} | Новые: {success_count} | Ошибки: {new_failed}")
                progress_window.update()
            
            progress_window.destroy()
            
            if success_count > 0:
                messagebox.showinfo("Результат", 
                                  f"Добавлено новых адресов: {success_count}\n"
                                  f"Не удалось обработать: {new_failed}")
            else:
                messagebox.showwarning("Предупреждение", "Не добавлено ни одного нового адреса")
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить файл:\n{str(e)}")

        self.update_address_count()

    def update_address_count(self):
        """Обновляет отображение количества адресов"""
        total_count = len([a for a in self.original_markers if a not in self.selected_markers])
        visible_count = self.all_addresses_list.size()
        self.all_addresses_frame.config(
            text=f"Все адреса ({visible_count}/{total_count})"
        )
        self.update_selected_count()

    def apply_filter(self, *args):
        """Применение фильтра без перезагрузки всех адресов"""
        filter_text = self.filter_var.get().lower()
        
        # Сохраняем текущую позицию прокрутки
        scroll_position = self.all_addresses_list.yview()
        
        # Очищаем только видимые элементы
        self.all_addresses_list.delete(0, tk.END)
        
        # Заполняем список с учетом фильтра
        for address in self.original_markers:
            if address not in self.selected_markers:  # Пропускаем избранные
                if not filter_text or filter_text in address.lower():
                    self.all_addresses_list.insert(tk.END, address)
    
        # Восстанавливаем позицию прокрутки
        self.all_addresses_list.yview_moveto(scroll_position[0])

        # Обновляем заголовок с количеством
        visible_count = self.all_addresses_list.size()
        total_count = len([a for a in self.original_markers if a not in self.selected_markers])
        self.update_address_count()

    def reset_filter(self):
        """Сброс фильтра без перезагрузки данных"""
        if self.filter_var.get():  # Только если фильтр не пустой
            self.filter_var.set("")
            # Не вызываем apply_filter, чтобы избежать двойной перезагрузки
    
    def delete_selected_failed(self):
        """Удаляет из необработанных с добавлением в удаленные"""
        selection = self.failed_addresses_list.curselection()
        if not selection:
            return
        
        for i in sorted(selection, reverse=True):
            address = self.failed_addresses_list.get(i)
            self.add_to_deleted(address)  # Добавляем в удаленные
            self.failed_addresses_list.delete(i)
            if address in self.failed_addresses:
                self.failed_addresses.remove(address)

    def delete_selected_from_all(self):
        """Удаляет выделенные адреса из основного списка"""
        selection = self.all_addresses_list.curselection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите адреса для удаления")
            return

        # Получаем выбранные адреса
        selected_addresses = [self.all_addresses_list.get(i) for i in selection]

        # Удаляем из списка (в обратном порядке, чтобы индексы не сдвигались)
        for i in sorted(selection, reverse=True):
            address = self.all_addresses_list.get(i)
            self.all_addresses_list.delete(i)

            # Удаляем маркер с карты и из всех хранилищ
            if address in self.markers:
                marker = self.markers.pop(address)  # Удаляем из общего хранилища
                if marker in self.map_widget.canvas_marker_list:
                    marker.delete()  # Удаляем с графического интерфейса
                if address in self.original_markers:
                    self.original_markers.pop(address)  # Удаляем из оригинального хранилища

            # Если маркер был в избранном, удаляем его из соответствующего хранилища
            if address in self.selected_markers:
                self.selected_markers.pop(address)
            elif address in self.selected_markers2:
                self.selected_markers2.pop(address)

        messagebox.showinfo("Успех", f"Удалено адресов: {len(selected_addresses)}")
        self.update_address_count()

    def update_deleted_list(self):
        """Обновляет отображение списка удаленных адресов"""
        self.deleted_addresses_list.delete(0, tk.END)
        for addr in self.deleted_addresses:
            self.deleted_addresses_list.insert(tk.END, addr)


    
    def geocode_address(self, address):
        """Получение координат для адреса с проверкой через Google и Яндекс"""
        if not address:
            return None, None
        
        # 1. Пробуем через Google Maps
        if self.gmaps_client:
            try:
                time.sleep(0.1)  # Ограничение запросов
                geocode_result = self.gmaps_client.geocode(address, region='ru', language='ru')
                
                if geocode_result:
                    location = geocode_result[0]['geometry']['location']
                    location_type = geocode_result[0]['geometry']['location_type']
                    
                    # Принимаем только точные результаты
                    if location_type in ['ROOFTOP', 'RANGE_INTERPOLATED']:
                        lat, lng = location['lat'], location['lng']
                        # Проверяем, что координаты в пределах России
                        if 41.0 <= lat <= 81.0 and 19.0 <= lng <= 180.0:
                            return lat, lng
            except Exception as e:
                print(f"Ошибка Google Geocoding: {e}")
        
        # 2. Если Google не нашел, пробуем через Яндекс
        if hasattr(self, 'yandex_geocoder'):
            lat, lng = self.yandex_geocoder.geocode(address)
            if lat and lng:
                return lat, lng
        
        return None, None
    
    def load_excel(self):
        """Загрузка данных из Excel файла"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        self.apply_filter()
        
        if not file_path:
            return
            
        try:
            df = pd.read_excel(file_path)
            
            self.clear_map()
            self.all_addresses_list.delete(0, tk.END)
            self.failed_addresses_list.delete(0, tk.END)
            self.failed_addresses = []
            
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Обработка адресов")
            progress_label = ttk.Label(progress_window, text="Получение координат...")
            progress_label.pack(pady=10)
            progress = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress.pack(pady=10)
            progress_window.update()
            
            total = len(df)
            processed = 0
            success_count = 0
            
            for _, row in df.iterrows():
                address = str(row.iloc[0]) if len(row) > 0 else ""
                
                if address and pd.notna(address) and address.strip():
                    lat, lng = None, None
                    
                    # Пробуем получить координаты из файла (если есть)
                    if len(row) >= 3:
                        try:
                            lat = float(row.iloc[1]) if pd.notna(row.iloc[1]) else None
                            lng = float(row.iloc[2]) if pd.notna(row.iloc[2]) else None
                        except (ValueError, TypeError):
                            pass
                    
                    
                    # Если координат нет в файле или они некорректны, геокодируем
                    if lat is None or lng is None or not self.validate_coordinates(lat, lng):
                        lat, lng = self.geocode_address(address)
                        
                        # Если Яндекс нашел, но Google нет - помечаем желтым
                        if lat and lng and not self.gmaps_client:
                            marker_color = "yellow"
                        else:
                            marker_color = None
                    else:
                        marker_color = None
                    
                    if lat is not None and lng is not None:
                        marker = self.map_widget.set_marker(
                            lat, lng, 
                            text=address,
                            marker_color_circle=marker_color,
                            marker_color_outside=marker_color
                        )



                    if lat is not None and lng is not None:
                        marker = self.map_widget.set_marker(lat, lng, text=address)
                        self.markers[address] = marker
                        self.original_markers[address] = marker
                        self.all_addresses_list.insert(tk.END, address)
                        success_count += 1
                    else:
                        self.process_failed_address(address)
                
                processed += 1
                progress['value'] = (processed / total) * 100
                progress_label.config(text=f"Обработано: {processed}/{total} | Успешно: {success_count}")
                progress_window.update()
            
            progress_window.destroy()
            
            if self.markers:
                first_marker = next(iter(self.markers.values()))
                self.map_widget.set_position(first_marker.position[0], first_marker.position[1])
                
            messagebox.showinfo("Результат", 
                              f"Успешно обработано: {success_count}\n"
                              f"Не удалось обработать: {len(self.failed_addresses)}")
            
        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить файл:\n{str(e)}")
        self.update_address_count()
    
    def suggest_similar_addresses(self, address):
        """Получение похожих вариантов адреса через Яндекс"""
        if not hasattr(self, 'yandex_geocoder'):
            return []
        
        url = "https://suggest-maps.yandex.ru/v1/suggest"
        params = {
            "apikey": self.YANDEX_MAPS_API_KEY,
            "text": address,
            "type": "geo",
            "lang": "ru_RU"
        }
        
        try:
            response = requests.get(url, params=params).json()
            return [item["display_name"] for item in response.get("results", [])[:5]]  # первые 5 вариантов
        except Exception as e:
            print(f"Ошибка подсказок Яндекс: {e}")
            return []
    
    def process_failed_address(self, address):
        """Обработка не найденных адресов с предложением вариантов"""
        # Пробуем найти похожие адреса
        suggestions = self.suggest_similar_addresses(address)
        
        if not suggestions:
            self.failed_addresses.append(address)
            self.failed_addresses_list.insert(tk.END, address)
            return
        
        # Создаем диалог с вариантами
        dialog = tk.Toplevel(self.root)
        dialog.title("Выберите вариант адреса")
        
        ttk.Label(dialog, text=f"Точный адрес '{address}' не найден. Возможно вы имели в виду:").pack(pady=10)
        
        listbox = tk.Listbox(dialog, width=80, height=5)
        listbox.pack(pady=5)
        
        for suggestion in suggestions:
            listbox.insert(tk.END, suggestion)
        
        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_address = listbox.get(selection[0])
                dialog.destroy()
                # Пробуем геокодировать выбранный вариант
                lat, lng = self.geocode_address(selected_address)
                if lat and lng:
                    marker = self.map_widget.set_marker(lat, lng, text=selected_address)
                    self.markers[selected_address] = marker
                    self.original_markers[selected_address] = marker
                    self.all_addresses_list.insert(tk.END, selected_address)
                else:
                    self.failed_addresses.append(address)
                    self.failed_addresses_list.insert(tk.END, address)
        
        ttk.Button(dialog, text="Выбрать", command=on_select).pack(pady=5)
        ttk.Button(dialog, text="Пропустить", command=lambda: [dialog.destroy(), 
            self.failed_addresses_list.insert(tk.END, address)]).pack(pady=5)


    
    def validate_coordinates(self, lat, lng):
        """Проверка корректности координат"""
        try:
            lat = float(lat)
            lng = float(lng)
            return -90 <= lat <= 90 and -180 <= lng <= 180
        except (ValueError, TypeError):
            return False
    
    def add_manual(self):
        """Добавление точки с ручным вводом координат"""
        address = self.address_entry.get().strip()
        lat = self.lat_entry.get().strip()
        lng = self.lng_entry.get().strip()

        if not address:
            messagebox.showwarning("Предупреждение", "Введите адрес")
            return
        
        # Если координаты введены - проверяем их
        if lat and lng:
            if not self.is_valid_coordinate(lat) or not self.is_valid_coordinate(lng):
                messagebox.showerror("Ошибка", "Введите корректные координаты")
                return
            lat = float(lat)
            lng = float(lng)
        else:
            # Если координаты не введены - геокодируем адрес
            lat, lng = self.geocode_address(address)
            if lat is None or lng is None:
                messagebox.showerror("Ошибка", "Не удалось определить координаты для адреса")
                self.failed_addresses.append(address)
                self.failed_addresses_list.insert(tk.END, address)
                return
        
        # Определяем цвет маркера
        marker_color = None
        if address in self.selected_markers:
            marker_color = "blue"
        elif address in self.selected_markers2:
            marker_color = "green"
        
        # Добавляем маркер на карту
        if address in self.markers:
            self.markers[address].delete()
        
        marker = self.map_widget.set_marker(
            lat, lng, 
            text=address,
            marker_color_circle=marker_color,
            marker_color_outside=marker_color,
            text_color=marker_color
        )
        self.markers[address] = marker
        self.original_markers[address] = marker
        
        if address not in self.selected_markers and address not in self.selected_markers2:
            self.all_addresses_list.insert(tk.END, address)
        
        # Очищаем поля ввода
        self.address_entry.delete(0, tk.END)
        self.lat_entry.delete(0, tk.END)
        self.lng_entry.delete(0, tk.END)
        self.update_counts()
    
    
    def remove_duplicates_from_all(self, address):
        """Удаляет дубликаты адреса из основного списка"""
        items = self.all_addresses_list.get(0, tk.END)
        for i in range(len(items)-1, -1, -1):
            if items[i] == address:
                self.all_addresses_list.delete(i)
    
    def return_to_all(self, event=None):
        """Возвращение адреса из избранного в основной список без дублирования"""
        selection = self.selected_addresses_list.curselection()
        if not selection:
            return
        
        address = self.selected_addresses_list.get(selection[0])
        
        if address not in self.selected_markers:
            return
        
        # Удаляем из избранного
        self.selected_addresses_list.delete(selection[0])
        marker = self.selected_markers.pop(address)
        
        # Восстанавливаем обычный маркер
        marker.delete()
        new_marker = self.map_widget.set_marker(
            marker.position[0], 
            marker.position[1], 
            text=address
        )
        self.markers[address] = new_marker
        
        # Добавляем в основной список только если:
        # 1. Нет активного фильтра ИЛИ
        # 2. Адрес соответствует текущему фильтру
        current_filter = self.filter_var.get().lower()
        if not current_filter or current_filter in address.lower():
            # Проверяем, что адреса еще нет в списке
            if address not in self.all_addresses_list.get(0, tk.END):
                self.all_addresses_list.insert(tk.END, address)
        
        # Обновляем original_markers
        self.original_markers[address] = new_marker
        self.update_selected_count()
        self.update_address_count() 
    
    def clear_map(self):
        """Полная очистка карты и всех списков"""
        # Удаляем все маркеры с карты
        for marker in list(self.map_widget.canvas_marker_list):
            marker.delete()
        
        # Очищаем все хранилища
        self.markers.clear()
        self.selected_markers.clear()
        self.selected_markers2.clear()
        self.original_markers.clear()
        self.failed_addresses.clear()
        
        # Очищаем списки
        self.all_addresses_list.delete(0, tk.END)
        self.selected_addresses_list.delete(0, tk.END)
        self.selected_addresses_list2.delete(0, tk.END)
        self.failed_addresses_list.delete(0, tk.END)
        self.deleted_addresses_list.delete(0, tk.END)
        self.deleted_addresses.clear()
    
    def setup_left_panel(self):
        """Левая панель управления"""
        ttk.Label(self.left_frame, text="Управление картой", font=('Arial', 10, 'bold')).pack(pady=10)
        
        # Кнопки управления
        ttk.Button(
            self.left_frame,
            text="Загрузить Excel",
            command=self.load_excel
        ).pack(pady=5, fill=tk.X)

        ttk.Button(
        self.left_frame, 
            text="Добавить адреса (к существующим)",
            command=self.load_additional_excel
        ).pack(pady=5, fill=tk.X)
        
        ttk.Button(
            self.left_frame,
            text="Удалить выделенные адреса",
            command=self.delete_selected_from_all
        ).pack(pady=5, fill=tk.X)
        
        ttk.Button(
            self.left_frame,
            text="Очистить карту",
            command=self.clear_map
        ).pack(pady=5, fill=tk.X)
        
        ttk.Button(
            self.left_frame,
            text="Сохранить избранное",
            command=self.save_selected_with_driver
        ).pack(pady=5, fill=tk.X)
        
        # Выбор типа карты
        ttk.Label(self.left_frame, text="Тип карты:").pack(pady=5)
        self.map_type = tk.StringVar(value="normal")
        ttk.Radiobutton(
            self.left_frame,
            text="Стандартная",
            variable=self.map_type,
            value="normal",
            command=self.change_map_type
        ).pack(anchor=tk.W)
        ttk.Radiobutton(
            self.left_frame,
            text="Спутник",
            variable=self.map_type,
            value="satellite",
            command=self.change_map_type
        ).pack(anchor=tk.W)

    def delete_selected_from_all(self):
        """Удаляет выделенные адреса из основного списка"""
        selection = self.all_addresses_list.curselection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите адреса для удаления")
            return
        
        # Получаем выбранные адреса
        selected_addresses = [self.all_addresses_list.get(i) for i in selection]
        
        # Удаляем из списка (в обратном порядке, чтобы индексы не сдвигались)
        for i in sorted(selection, reverse=True):
            address = self.all_addresses_list.get(i)
            self.all_addresses_list.delete(i)
            
            # Удаляем маркер с карты, если он не в избранном
            if address in self.markers and address not in self.selected_markers:
                self.markers[address].delete()
                del self.markers[address]
                del self.original_markers[address]
        
        messagebox.showinfo("Успех", f"Удалено адресов: {len(selected_addresses)}")

    
    def change_map_type(self):
        """Изменение типа карты"""
        map_type = self.map_type.get()
        if map_type == "normal":
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=m&hl=en&x={x}&y={y}&z={z}")
        elif map_type == "satellite":
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=s&hl=en&x={x}&y={y}&z={z}")
    
    def save_selected_with_driver(self):
        """Сохранение выбранного списка с водителем (исправленная версия)"""
        if self.list_selector.get() == "list1":
            selected_dict = self.selected_markers
            list_widget = self.selected_addresses_list
        else:
            selected_dict = self.selected_markers2
            list_widget = self.selected_addresses_list2

        if not selected_dict:
            messagebox.showwarning("Предупреждение", "Нет адресов в выбранном списке")
            return

        driver = self.driver_var.get()
        if not driver:
            messagebox.showwarning("Предупреждение", "Выберите водителя")
            return

        default_filename = f"маршруты_{datetime.now().strftime('%Y-%m-%d')}.xlsx"
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            initialfile=default_filename,
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if not file_path:
            return

        try:
            # Создаем DataFrame с данными
            data = []
            # Добавляем заголовки столбцов
            data.append({
                'Адрес': "Адрес",
                'Широта': "Широта",
                'Долгота': "Долгота"
            })
            # Добавляем пустую строку
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем строку с ФИО водителя
            data.append({
                'Адрес': f"Водитель: {driver}",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем пустую строку после водителя
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем сами адреса
            for address, marker in selected_dict.items():
                data.append({
                    'Адрес': address,
                    'Широта': marker.position[0],
                    'Долгота': marker.position[1]
                })
            # Добавляем пустую строку в конце
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            df = pd.DataFrame(data)

            # Проверяем, существует ли файл
            if os.path.exists(file_path):
                existing_df = pd.read_excel(file_path)
                # Находим все индексы строк с водителями
                driver_indices = []
                for i, row in existing_df.iterrows():
                    if isinstance(row['Адрес'], str) and row['Адрес'].startswith('Водитель:'):
                        driver_indices.append(i)
                if driver_indices:
                    # Если есть водители, добавляем после последнего
                    last_driver_index = driver_indices[-1]
                    # Находим следующую пустую строку после последнего водителя
                    next_empty_index = last_driver_index + 1
                    while next_empty_index < len(existing_df) and existing_df.iloc[next_empty_index]['Адрес'] != "":
                        next_empty_index += 1
                    # Вставляем новые данные после последнего водителя
                    part1 = existing_df.iloc[:next_empty_index+1]
                    part2 = existing_df.iloc[next_empty_index+1:]
                    df = pd.concat([part1, df.iloc[2:]], ignore_index=True)
                else:
                    # Если водителей нет, добавляем в конец
                    df = pd.concat([existing_df, df.iloc[1:]], ignore_index=True)

            # Сохраняем файл с форматированием
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False)
                # Настраиваем ширину столбцов
                worksheet = writer.sheets['Sheet1']
                worksheet.column_dimensions['A'].width = 50  # Адрес
                worksheet.column_dimensions['B'].width = 15  # Широта
                worksheet.column_dimensions['C'].width = 15  # Долгота

            # Удаление маркеров
            addresses_to_remove = list(selected_dict.keys())
            for address in addresses_to_remove:
                marker = selected_dict.get(address)
                if marker:
                    # Удаление из всех возможных хранилищ
                    for storage in [self.markers, self.original_markers, 
                                  self.selected_markers, self.selected_markers2]:
                        storage.pop(address, None)
                    
                    # Гарантированное удаление с карты
                    if marker in self.map_widget.canvas_marker_list:
                        marker.delete()
                        self.map_widget.canvas_marker_list.remove(marker)
            
            # Принудительное обновление карты
            self.map_widget.update()

            # Очищаем список
            list_widget.delete(0, tk.END)
            self.driver_var.set('')
            self.update_counts()

            messagebox.showinfo("Успех", f"Сохранено {len(data)-5} адресов для водителя {driver}")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сохранить файл:{str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = MapApp(root)
    root.mainloop()
