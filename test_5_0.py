"""
Главный файл приложения для управления адресами и маршрутами
Версия 5.0 - Модульная архитектура

Основные возможности:
- Загрузка и отображение адресов на карте
- Автоматическое распределение адресов по группам
- Управление водителями и расписанием
- Система задач
- Кэширование геокодирования
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkintermapview
import requests
import pandas as pd
import os
import math
from typing import Dict, List, Tuple, Set, Optional, Any
import ttkbootstrap as ttkb
from ttkbootstrap import Style

# Импорт модулей
from distribution_algorithm import DistributionAlgorithm
from color_manager import ColorManager
from data_manager import DataManager
from ui_dialogs import AddressSelectionDialog, DistributionSettingsDialog, ModernMessageBox, DriverSaveDialog, TaskDialog

# Константы
MINSK_CENTER = (53.9045, 27.5615)
GOOGLE_API_KEY = "AIzaSyDHGBtZ8XiJnmPVPVGpGqBGOGED2jKjdgM"
YANDEX_API_KEY = "29294198-6cdc-4a11-8478-7bb2d5405b63"


class AddressManager:
    """Главный класс приложения для управления адресами"""

    def __init__(self):
        # Инициализация модулей
        self.data_manager = DataManager()
        self.distribution_algorithm = DistributionAlgorithm()
        self.color_manager = ColorManager()

        # Основные переменные
        self.markers = {}
        self.selected_markers = {}
        self.selected_markers2 = {}
        self.deleted_addresses = []
        self.unprocessed_addresses = []

        # Счетчики
        self.total_sum = 0.0
        self.total_weight = 0.0
        self.list1_sum = 0.0
        self.list1_weight = 0.0
        self.list2_sum = 0.0
        self.list2_weight = 0.0

        # UI переменные
        self.current_tab = 0

        # Инициализация UI
        self.setup_ui()

        # Загрузка данных
        self.data_manager.load_all_data()

        # Инициализация цветов
        self.color_manager.initialize_used_colors(self.data_manager.saved_drivers)

        # Обновляем отображение задач
        self.update_tasks_display()

        print("Приложение инициализировано")

    def setup_ui(self):
        """Настройка пользовательского интерфейса"""
        # Создание главного окна
        self.root = ttkb.Window(themename="darkly")
        self.root.title("Управление адресами и маршрутами v5.0")
        self.root.geometry("1400x900")

        # Настройка стиля
        self.style = Style()

        # Создание основного интерфейса
        self.create_main_interface()

        # Привязка событий
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_main_interface(self):
        """Создает основной интерфейс приложения"""
        # Главный контейнер
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Левая панель с вкладками
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))

        # Создание вкладок
        self.notebook = ttk.Notebook(left_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Вкладка "Задачи"
        self.create_tasks_tab()

        # Вкладка "Расписание"
        self.create_schedule_tab()

        # Вкладка "Сервис"
        self.create_service_tab()

        # Правая панель с картой
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Создание карты
        self.create_map(right_frame)

        # Создание панели управления картой
        self.create_map_controls(right_frame)

        # Создание панели счетчиков
        self.create_counters_panel(right_frame)

    def create_tasks_tab(self):
        """Создает вкладку задач"""
        tasks_frame = ttk.Frame(self.notebook)
        self.notebook.add(tasks_frame, text="Задачи")

        # Список задач
        tasks_list_frame = ttk.LabelFrame(tasks_frame, text="Список задач")
        tasks_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Создание Treeview для задач
        columns = ("ID", "Дата", "Задача", "Водитель", "Выполнено")
        self.tasks_tree = ttk.Treeview(tasks_list_frame, columns=columns, show="headings", height=15)

        # Настройка колонок
        self.tasks_tree.heading("ID", text="ID")
        self.tasks_tree.heading("Дата", text="Дата")
        self.tasks_tree.heading("Задача", text="Задача")
        self.tasks_tree.heading("Водитель", text="Водитель")
        self.tasks_tree.heading("Выполнено", text="Выполнено")

        self.tasks_tree.column("ID", width=50)
        self.tasks_tree.column("Дата", width=80)
        self.tasks_tree.column("Задача", width=200)
        self.tasks_tree.column("Водитель", width=110)
        self.tasks_tree.column("Выполнено", width=80)

        # Скроллбар для задач
        tasks_scrollbar = ttk.Scrollbar(tasks_list_frame, orient="vertical", command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)

        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Кнопки управления задачами
        tasks_buttons_frame = ttk.Frame(tasks_frame)
        tasks_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(tasks_buttons_frame, text="Добавить задачу",
                  command=self.add_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(tasks_buttons_frame, text="Редактировать",
                  command=self.edit_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(tasks_buttons_frame, text="Удалить",
                  command=self.delete_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(tasks_buttons_frame, text="Выполнено",
                  command=self.mark_task_completed).pack(side=tk.LEFT, padx=2)

    def create_schedule_tab(self):
        """Создает вкладку расписания"""
        schedule_frame = ttk.Frame(self.notebook)
        self.notebook.add(schedule_frame, text="Расписание")

        # Список расписания
        schedule_list_frame = ttk.LabelFrame(schedule_frame, text="Расписание водителей")
        schedule_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Создание Treeview для расписания
        columns = ("День", "Регион", "Водитель", "Количество", "Сумма")
        self.schedule_tree = ttk.Treeview(schedule_list_frame, columns=columns, show="headings", height=15)

        # Настройка колонок
        self.schedule_tree.heading("День", text="День")
        self.schedule_tree.heading("Регион", text="Регион")
        self.schedule_tree.heading("Водитель", text="Водитель")
        self.schedule_tree.heading("Количество", text="Кол-во")
        self.schedule_tree.heading("Сумма", text="Сумма")

        self.schedule_tree.column("День", width=100)
        self.schedule_tree.column("Регион", width=120)
        self.schedule_tree.column("Водитель", width=100)
        self.schedule_tree.column("Количество", width=80)
        self.schedule_tree.column("Сумма", width=100)

        # Скроллбар для расписания
        schedule_scrollbar = ttk.Scrollbar(schedule_list_frame, orient="vertical", command=self.schedule_tree.yview)
        self.schedule_tree.configure(yscrollcommand=schedule_scrollbar.set)

        self.schedule_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        schedule_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Кнопки управления расписанием
        schedule_buttons_frame = ttk.Frame(schedule_frame)
        schedule_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(schedule_buttons_frame, text="Добавить регион",
                  command=self.add_schedule_region).pack(side=tk.LEFT, padx=2)
        ttk.Button(schedule_buttons_frame, text="Редактировать",
                  command=self.edit_schedule_entry).pack(side=tk.LEFT, padx=2)
        ttk.Button(schedule_buttons_frame, text="Удалить",
                  command=self.delete_schedule_entry).pack(side=tk.LEFT, padx=2)

    def create_service_tab(self):
        """Создает вкладку сервиса"""
        service_frame = ttk.Frame(self.notebook)
        self.notebook.add(service_frame, text="Сервис")

        # Загрузка файлов
        file_frame = ttk.LabelFrame(service_frame, text="Загрузка данных")
        file_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(file_frame, text="Загрузить Excel",
                  command=self.load_excel_file).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(file_frame, text="Очистить карту",
                  command=self.clear_map).pack(side=tk.LEFT, padx=5, pady=5)

        # Ручной ввод адреса
        manual_frame = ttk.LabelFrame(service_frame, text="Ручной ввод адреса")
        manual_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(manual_frame, text="Адрес:").pack(anchor="w", padx=5)
        self.manual_address_entry = ttk.Entry(manual_frame, width=50)
        self.manual_address_entry.pack(fill=tk.X, padx=5, pady=2)

        ttk.Button(manual_frame, text="Добавить на карту",
                  command=self.add_manual_address).pack(pady=5)

        # Настройки карты
        map_settings_frame = ttk.LabelFrame(service_frame, text="Настройки карты")
        map_settings_frame.pack(fill=tk.X, padx=5, pady=5)

        # Тип карты
        self.map_type_var = tk.StringVar(value="normal")
        ttk.Radiobutton(map_settings_frame, text="Обычная", variable=self.map_type_var,
                       value="normal", command=self.change_map_type).pack(anchor="w", padx=5)
        ttk.Radiobutton(map_settings_frame, text="Спутник", variable=self.map_type_var,
                       value="satellite", command=self.change_map_type).pack(anchor="w", padx=5)

        # Списки адресов
        addresses_frame = ttk.LabelFrame(service_frame, text="Управление адресами")
        addresses_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Необработанные адреса
        unprocessed_frame = ttk.LabelFrame(addresses_frame, text="Необработанные адреса")
        unprocessed_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        self.unprocessed_listbox = tk.Listbox(unprocessed_frame, height=6)
        self.unprocessed_listbox.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # Удаленные адреса
        deleted_frame = ttk.LabelFrame(addresses_frame, text="Удаленные адреса")
        deleted_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        self.deleted_listbox = tk.Listbox(deleted_frame, height=6)
        self.deleted_listbox.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

    def create_map(self, parent):
        """Создает карту"""
        map_frame = ttk.Frame(parent)
        map_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # Создание карты
        self.map_widget = tkintermapview.TkinterMapView(map_frame, width=800, height=600, corner_radius=0)
        self.map_widget.pack(fill=tk.BOTH, expand=True)

        # Установка начальной позиции на Минск
        self.map_widget.set_position(MINSK_CENTER[0], MINSK_CENTER[1])
        self.map_widget.set_zoom(11)

    def create_map_controls(self, parent):
        """Создает панель управления картой"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=5)

        # Левая группа кнопок
        left_controls = ttk.Frame(controls_frame)
        left_controls.pack(side=tk.LEFT)

        ttk.Button(left_controls, text="Список 1",
                  command=self.show_list1_addresses).pack(side=tk.LEFT, padx=2)
        ttk.Button(left_controls, text="Список 2",
                  command=self.show_list2_addresses).pack(side=tk.LEFT, padx=2)

        # Центральная группа - распределение
        center_controls = ttk.Frame(controls_frame)
        center_controls.pack(side=tk.LEFT, padx=20)

        self.distribute_button = ttk.Button(center_controls, text="Распределить",
                                          command=self.handle_distribution_button)
        self.distribute_button.pack(side=tk.LEFT, padx=2)

        ttk.Button(center_controls, text="Настройки",
                  command=self.show_distribution_settings).pack(side=tk.LEFT, padx=2)

        # Правая группа кнопок
        right_controls = ttk.Frame(controls_frame)
        right_controls.pack(side=tk.RIGHT)

        ttk.Button(right_controls, text="Сохранить водителя",
                  command=self.save_driver).pack(side=tk.LEFT, padx=2)
        ttk.Button(right_controls, text="Загрузить водителя",
                  command=self.load_driver).pack(side=tk.LEFT, padx=2)

    def create_counters_panel(self, parent):
        """Создает панель счетчиков"""
        counters_frame = ttk.LabelFrame(parent, text="Статистика")
        counters_frame.pack(fill=tk.X, pady=5)

        # Создаем фреймы для каждого списка
        lists_frame = ttk.Frame(counters_frame)
        lists_frame.pack(fill=tk.X, padx=10, pady=5)

        # Список 1
        list1_frame = ttk.LabelFrame(lists_frame, text="Список 1")
        list1_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.list1_count_label = ttk.Label(list1_frame, text="Адресов: 0")
        self.list1_count_label.pack(anchor="w", padx=5)

        self.list1_sum_label = ttk.Label(list1_frame, text="Сумма: 0.00")
        self.list1_sum_label.pack(anchor="w", padx=5)

        self.list1_weight_label = ttk.Label(list1_frame, text="Вес: 0.000")
        self.list1_weight_label.pack(anchor="w", padx=5)

        # Список 2
        list2_frame = ttk.LabelFrame(lists_frame, text="Список 2")
        list2_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.list2_count_label = ttk.Label(list2_frame, text="Адресов: 0")
        self.list2_count_label.pack(anchor="w", padx=5)

        self.list2_sum_label = ttk.Label(list2_frame, text="Сумма: 0.00")
        self.list2_sum_label.pack(anchor="w", padx=5)

        self.list2_weight_label = ttk.Label(list2_frame, text="Вес: 0.000")
        self.list2_weight_label.pack(anchor="w", padx=5)

        # Общая статистика
        total_frame = ttk.LabelFrame(lists_frame, text="Всего")
        total_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.total_count_label = ttk.Label(total_frame, text="Адресов: 0")
        self.total_count_label.pack(anchor="w", padx=5)

        self.total_sum_label = ttk.Label(total_frame, text="Сумма: 0.00")
        self.total_sum_label.pack(anchor="w", padx=5)

        self.total_weight_label = ttk.Label(total_frame, text="Вес: 0.000")
        self.total_weight_label.pack(anchor="w", padx=5)

    def load_excel_file(self):
        """Загружает адреса из Excel файла"""
        file_path = filedialog.askopenfilename(
            title="Выберите Excel файл",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )

        if not file_path:
            return

        try:
            print(f"📂 Загружаем файл: {file_path}")
            df = pd.read_excel(file_path)
            print(f"📊 Найдено {len(df)} строк в файле")
            print(f"📋 Колонки в файле: {list(df.columns)}")

            # Определяем колонку с адресами
            address_column = None
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['адрес', 'address', 'разгрузки']):
                    address_column = col
                    print(f"✅ Найдена колонка с адресами: '{address_column}'")
                    break

            if not address_column:
                ModernMessageBox.show_error(self.root, "Ошибка", "Не найдена колонка с адресами")
                return

            # Загружаем адреса
            addresses_loaded = 0
            print(f"🔄 Начинаем обработку адресов...")

            for index, row in df.iterrows():
                address = row[address_column]
                if pd.notna(address) and address.strip():
                    # Извлекаем значения суммы и веса если есть
                    sum_value = 0.0
                    weight_value = 0.0

                    for col in df.columns:
                        if 'сумма' in col.lower() or 'sum' in col.lower():
                            sum_value = float(row[col]) if pd.notna(row[col]) else 0.0
                        elif 'вес' in col.lower() or 'weight' in col.lower():
                            weight_value = float(row[col]) if pd.notna(row[col]) else 0.0

                    # Сохраняем значения
                    self.data_manager.set_address_value(address.strip(), 'sum', sum_value)
                    self.data_manager.set_address_value(address.strip(), 'weight', weight_value)

                    # Добавляем на карту
                    self.add_address_to_map(address.strip())
                    addresses_loaded += 1

                    # Показываем прогресс каждые 10 адресов
                    if addresses_loaded % 10 == 0:
                        print(f"📊 Обработано {addresses_loaded} адресов...")

            ModernMessageBox.show_info(self.root, "Успех", f"Загружено {addresses_loaded} адресов")
            self.update_counts()

        except Exception as e:
            ModernMessageBox.show_error(self.root, "Ошибка", f"Не удалось загрузить файл:\n{str(e)}")
            print(f"Ошибка загрузки Excel: {e}")

    def add_address_to_map(self, address: str):
        """Добавляет адрес на карту"""
        if address in self.markers:
            print(f"Адрес уже на карте: {address}")
            return

        # Проверяем кэш
        if address in self.data_manager.geocode_cache:
            lat, lng = self.data_manager.geocode_cache[address]
            self.create_marker(address, lat, lng)
            return

        # Геокодирование через Google
        try:
            response = requests.get(
                "https://maps.googleapis.com/maps/api/geocode/json",
                params={
                    "address": f"{address}, Минск, Беларусь",
                    "key": GOOGLE_API_KEY
                },
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data["results"]:
                    location = data["results"][0]["geometry"]["location"]
                    lat, lng = location["lat"], location["lng"]

                    # Сохраняем в кэш
                    self.data_manager.geocode_cache[address] = (lat, lng)

                    self.create_marker(address, lat, lng)
                    return
        except Exception as e:
            print(f"Ошибка Google геокодирования: {e}")

        # Если Google не сработал, пробуем Yandex
        try:
            response = requests.get(
                "https://geocode-maps.yandex.ru/1.x/",
                params={
                    "apikey": YANDEX_API_KEY,
                    "geocode": f"Минск, {address}",
                    "format": "json"
                },
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data["response"]["GeoObjectCollection"]["featureMember"]:
                    pos = data["response"]["GeoObjectCollection"]["featureMember"][0]["GeoObject"]["Point"]["pos"]
                    lng, lat = map(float, pos.split())

                    # Сохраняем в кэш
                    self.data_manager.geocode_cache[address] = (lat, lng)

                    self.create_marker(address, lat, lng)
                    return
        except Exception as e:
            print(f"Ошибка Yandex геокодирования: {e}")

        # Если геокодирование не удалось
        print(f"Не удалось геокодировать адрес: {address}")
        self.unprocessed_addresses.append(address)
        self.update_unprocessed_list()

    def create_marker(self, address: str, lat: float, lng: float):
        """Создает маркер на карте"""
        try:
            marker = self.map_widget.set_marker(lat, lng, text=address, marker_color_circle="brown")
            self.markers[address] = marker

            # Привязываем события (проверяем доступность методов)
            if hasattr(marker, 'add_function'):
                marker.add_function(lambda: self.on_marker_click(address))
            elif hasattr(marker, 'command'):
                marker.command = lambda: self.on_marker_click(address)
            else:
                print(f"⚠️ Не найден способ привязки событий к маркеру")

            # Добавляем контекстное меню (если поддерживается)
            if hasattr(marker, 'add_right_click_menu_command'):
                marker.add_right_click_menu_command(label="Добавить в список 1",
                                                  command=lambda: self.add_to_list1(address))
                marker.add_right_click_menu_command(label="Добавить в список 2",
                                                  command=lambda: self.add_to_list2(address))
                marker.add_right_click_menu_command(label="Удалить адрес",
                                                  command=lambda: self.delete_address(address))

            # print(f"✅ Создан маркер для адреса: {address}")  # Отключено для уменьшения вывода

        except Exception as e:
            print(f"❌ Ошибка создания маркера для {address}: {e}")
            # Добавляем в необработанные
            if address not in self.unprocessed_addresses:
                self.unprocessed_addresses.append(address)
                self.update_unprocessed_list()

    def on_marker_click(self, address: str):
        """Обработчик клика по маркеру"""
        if address in self.selected_markers:
            # Убираем из списка 1
            self.remove_from_list1(address)
        elif address in self.selected_markers2:
            # Убираем из списка 2
            self.remove_from_list2(address)
        else:
            # Добавляем в список 1
            self.add_to_list1(address)

    def add_to_list1(self, address: str):
        """Добавляет адрес в список 1"""
        if address not in self.markers:
            return

        marker = self.markers[address]
        self.selected_markers[address] = marker

        # Меняем цвет маркера (проверяем доступные методы)
        try:
            if hasattr(marker, 'marker_color_circle'):
                marker.marker_color_circle = "blue"
            elif hasattr(marker, 'set_color'):
                marker.set_color("blue")
            elif hasattr(marker, 'change_color'):
                marker.change_color("blue")
        except Exception as e:
            print(f"⚠️ Не удалось изменить цвет маркера: {e}")

        self.update_counts()
        print(f"Добавлен в список 1: {address}")

    def remove_from_list1(self, address: str):
        """Убирает адрес из списка 1"""
        if address in self.selected_markers:
            marker = self.selected_markers[address]
            del self.selected_markers[address]

            # Возвращаем исходный цвет
            try:
                if hasattr(marker, 'marker_color_circle'):
                    marker.marker_color_circle = "brown"
                elif hasattr(marker, 'set_color'):
                    marker.set_color("brown")
                elif hasattr(marker, 'change_color'):
                    marker.change_color("brown")
            except Exception as e:
                print(f"⚠️ Не удалось изменить цвет маркера: {e}")

            # Отмечаем как вручную убранный если идет распределение
            if hasattr(self.distribution_algorithm, 'distribution_params') and self.distribution_algorithm.distribution_params:
                self.distribution_algorithm.mark_manually_processed([address])
                self.distribution_algorithm.update_available_addresses(
                    self.markers, self.selected_markers, self.selected_markers2
                )

            self.update_counts()
            print(f"Убран из списка 1: {address}")

    def add_to_list2(self, address: str):
        """Добавляет адрес в список 2"""
        if address not in self.markers:
            return

        marker = self.markers[address]
        self.selected_markers2[address] = marker

        # Меняем цвет маркера
        try:
            if hasattr(marker, 'marker_color_circle'):
                marker.marker_color_circle = "green"
            elif hasattr(marker, 'set_color'):
                marker.set_color("green")
            elif hasattr(marker, 'change_color'):
                marker.change_color("green")
        except Exception as e:
            print(f"⚠️ Не удалось изменить цвет маркера: {e}")

        self.update_counts()
        print(f"Добавлен в список 2: {address}")

    def remove_from_list2(self, address: str):
        """Убирает адрес из списка 2"""
        if address in self.selected_markers2:
            marker = self.selected_markers2[address]
            del self.selected_markers2[address]

            # Возвращаем исходный цвет
            try:
                if hasattr(marker, 'marker_color_circle'):
                    marker.marker_color_circle = "brown"
                elif hasattr(marker, 'set_color'):
                    marker.set_color("brown")
                elif hasattr(marker, 'change_color'):
                    marker.change_color("brown")
            except Exception as e:
                print(f"⚠️ Не удалось изменить цвет маркера: {e}")

            # Отмечаем как вручную убранный если идет распределение
            if hasattr(self.distribution_algorithm, 'distribution_params') and self.distribution_algorithm.distribution_params:
                self.distribution_algorithm.mark_manually_processed([address])
                self.distribution_algorithm.update_available_addresses(
                    self.markers, self.selected_markers, self.selected_markers2
                )

            self.update_counts()
            print(f"Убран из списка 2: {address}")

    def delete_address(self, address: str):
        """Удаляет адрес с карты"""
        if address in self.markers:
            # Убираем из всех списков
            if address in self.selected_markers:
                del self.selected_markers[address]
            if address in self.selected_markers2:
                del self.selected_markers2[address]

            # Удаляем маркер
            self.markers[address].delete()
            del self.markers[address]

            # Добавляем в список удаленных
            if address not in self.deleted_addresses:
                self.deleted_addresses.append(address)
                self.update_deleted_list()

            self.update_counts()
            print(f"Удален адрес: {address}")

    def update_deleted_list(self):
        """Обновляет список удаленных адресов"""
        self.deleted_listbox.delete(0, tk.END)
        for address in self.deleted_addresses:
            self.deleted_listbox.insert(tk.END, address)

    def update_counts(self):
        """Обновляет счетчики"""
        # Подсчет для списка 1
        self.list1_sum = sum(self.data_manager.get_address_value(addr, 'sum') for addr in self.selected_markers)
        self.list1_weight = sum(self.data_manager.get_address_value(addr, 'weight') for addr in self.selected_markers)

        # Подсчет для списка 2
        self.list2_sum = sum(self.data_manager.get_address_value(addr, 'sum') for addr in self.selected_markers2)
        self.list2_weight = sum(self.data_manager.get_address_value(addr, 'weight') for addr in self.selected_markers2)

        # Общий подсчет
        self.total_sum = sum(self.data_manager.get_address_value(addr, 'sum') for addr in self.markers)
        self.total_weight = sum(self.data_manager.get_address_value(addr, 'weight') for addr in self.markers)

        # Обновляем UI
        if hasattr(self, 'list1_count_label'):
            self.list1_count_label.config(text=f"Адресов: {len(self.selected_markers)}")
            self.list1_sum_label.config(text=f"Сумма: {self.list1_sum:.2f}")
            self.list1_weight_label.config(text=f"Вес: {self.list1_weight:.3f}")

            self.list2_count_label.config(text=f"Адресов: {len(self.selected_markers2)}")
            self.list2_sum_label.config(text=f"Сумма: {self.list2_sum:.2f}")
            self.list2_weight_label.config(text=f"Вес: {self.list2_weight:.3f}")

            self.total_count_label.config(text=f"Адресов: {len(self.markers)}")
            self.total_sum_label.config(text=f"Сумма: {self.total_sum:.2f}")
            self.total_weight_label.config(text=f"Вес: {self.total_weight:.3f}")

        print(f"Список 1: {len(self.selected_markers)} адресов, сумма: {self.list1_sum:.2f}, вес: {self.list1_weight:.3f}")
        print(f"Список 2: {len(self.selected_markers2)} адресов, сумма: {self.list2_sum:.2f}, вес: {self.list2_weight:.3f}")

    def update_unprocessed_list(self):
        """Обновляет список необработанных адресов"""
        self.unprocessed_listbox.delete(0, tk.END)
        for address in self.unprocessed_addresses:
            self.unprocessed_listbox.insert(tk.END, address)

    def clear_map(self):
        """Очищает карту"""
        for marker in self.markers.values():
            marker.delete()

        self.markers.clear()
        self.selected_markers.clear()
        self.selected_markers2.clear()
        self.unprocessed_addresses.clear()
        self.deleted_addresses.clear()

        self.update_counts()
        self.update_unprocessed_list()

        ModernMessageBox.show_info(self.root, "Успех", "Карта очищена")

    def add_manual_address(self):
        """Добавляет адрес вручную"""
        address = self.manual_address_entry.get().strip()
        if not address:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Введите адрес")
            return

        self.add_address_to_map(address)
        self.manual_address_entry.delete(0, tk.END)

    def change_map_type(self):
        """Изменяет тип карты"""
        map_type = self.map_type_var.get()
        if map_type == "satellite":
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=s&hl=en&x={x}&y={y}&z={z}&s=Ga", max_zoom=22)
        else:
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=m&hl=en&x={x}&y={y}&z={z}&s=Ga", max_zoom=22)

    def handle_distribution_button(self):
        """Обработчик кнопки распределения"""
        button_text = self.distribute_button.cget("text")

        if button_text == "Распределить":
            self.start_distribution()
        elif button_text == "Продолжить":
            self.continue_distribution()

    def start_distribution(self):
        """Запускает автоматическое распределение адресов"""
        if not self.markers:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Нет адресов для распределения")
            return

        # Проверяем, что список 1 пуст
        if self.selected_markers:
            ModernMessageBox.show_warning(self.root, "Предупреждение",
                                        "Очистите список 1 перед началом распределения")
            return

        # Собираем доступные адреса
        available_addresses = []
        for address, marker in self.markers.items():
            if address not in self.selected_markers and address not in self.selected_markers2:
                try:
                    lat, lng = marker.position
                    available_addresses.append({
                        'address': address,
                        'lat': lat,
                        'lng': lng,
                        'marker': marker
                    })
                except Exception as e:
                    print(f"Ошибка с маркером для {address}: {e}")

        if not available_addresses:
            ModernMessageBox.show_info(self.root, "Информация", "Нет доступных адресов для распределения")
            return

        # Настройки по умолчанию
        settings = {
            'max_radius': 15,
            'sector_width': 30,
            'group_size': 8
        }

        # Инициализируем распределение
        self.distribution_algorithm.initialize_distribution(
            available_addresses,
            settings['max_radius'],
            settings['sector_width'],
            settings['group_size']
        )

        # Получаем первую группу
        self.distribute_next_group()

    def distribute_next_group(self):
        """Распределяет следующую группу адресов"""
        if not hasattr(self.distribution_algorithm, 'distribution_params') or not self.distribution_algorithm.distribution_params:
            ModernMessageBox.show_warning(self.root, "Ошибка", "Распределение не инициализировано")
            return

        # Получаем следующую группу
        group = self.distribution_algorithm.get_next_group()

        if not group:
            ModernMessageBox.show_info(self.root, "Завершено", "Все адреса распределены")
            self.distribution_algorithm.clear_session_colors()
            self.distribute_button.config(text="Распределить")
            return

        # Получаем цвет для группы
        color = self.color_manager.get_next_available_color(
            saved_drivers=self.data_manager.saved_drivers,
            current_session_colors=self.distribution_algorithm.get_session_colors(),
            all_markers=self.markers,
            selected_markers=self.selected_markers,
            selected_markers2=self.selected_markers2,
            map_widget=self.map_widget
        )

        # Добавляем цвет в сессию
        self.distribution_algorithm.add_session_color(color)

        # Добавляем адреса в список 1 с назначенным цветом
        for addr_info in group:
            address = addr_info['address']
            marker = addr_info['marker']

            self.selected_markers[address] = marker

            # Изменяем цвет маркера
            try:
                if hasattr(marker, 'marker_color_circle'):
                    marker.marker_color_circle = color
                elif hasattr(marker, 'set_color'):
                    marker.set_color(color)
                elif hasattr(marker, 'change_color'):
                    marker.change_color(color)
            except Exception as e:
                print(f"⚠️ Не удалось изменить цвет маркера на {color}: {e}")

        print(f"🎨 Назначен цвет для группы: {color}")
        print(f"📦 Добавлено в список 1: {len(group)} адресов")

        # Обновляем кнопку
        self.distribute_button.config(text="Продолжить")

        self.update_counts()

    def show_distribution_settings(self):
        """Показывает диалог настроек распределения"""
        current_settings = {
            'max_radius': 15,
            'sector_width': 30,
            'group_size': 8
        }

        dialog = DistributionSettingsDialog(self.root, current_settings)
        result = dialog.wait_for_result()

        if result:
            print(f"Новые настройки распределения: {result}")

    def continue_distribution(self):
        """Продолжает распределение следующей группы"""
        # Проверяем, что список 1 пуст (водитель назначен и сохранен)
        if self.selected_markers:
            ModernMessageBox.show_warning(self.root, "Список не пуст",
                                        "Сначала назначьте водителя и сохраните текущие адреса")
            return

        # Проверяем, есть ли вручную убранные адреса для возможного включения
        self.check_manually_removed_addresses()

        # Продолжаем распределение
        self.distribute_next_group()

    def check_manually_removed_addresses(self):
        """Проверяет наличие вручную убранных адресов и предлагает их включить в распределение"""
        manually_removed = self.distribution_algorithm.get_manually_removed_addresses(
            self.markers, self.selected_markers, self.selected_markers2
        )

        if not manually_removed:
            print("ℹ️ Нет вручную убранных адресов для включения в распределение")
            return

        print(f"🔍 Найдено {len(manually_removed)} вручную убранных адресов")

        # Показываем диалог выбора адресов
        def on_apply(selected_addresses):
            self.distribution_algorithm.restore_addresses_to_distribution(selected_addresses)
            if selected_addresses:
                self.distribution_algorithm.update_available_addresses(
                    self.markers, self.selected_markers, self.selected_markers2
                )

        dialog = AddressSelectionDialog(self.root, manually_removed, on_apply)
        dialog.wait_for_result()

    def show_list1_addresses(self):
        """Показывает адреса из списка 1"""
        if not self.selected_markers:
            ModernMessageBox.show_info(self.root, "Список 1", "Список 1 пуст")
            return

        addresses = list(self.selected_markers.keys())
        message = f"Адреса в списке 1 ({len(addresses)}):\n\n" + "\n".join(addresses[:10])
        if len(addresses) > 10:
            message += f"\n... и еще {len(addresses) - 10} адресов"

        ModernMessageBox.show_info(self.root, "Список 1", message)

    def show_list2_addresses(self):
        """Показывает адреса из списка 2"""
        if not self.selected_markers2:
            ModernMessageBox.show_info(self.root, "Список 2", "Список 2 пуст")
            return

        addresses = list(self.selected_markers2.keys())
        message = f"Адреса в списке 2 ({len(addresses)}):\n\n" + "\n".join(addresses[:10])
        if len(addresses) > 10:
            message += f"\n... и еще {len(addresses) - 10} адресов"

        ModernMessageBox.show_info(self.root, "Список 2", message)

    def save_driver(self):
        """Сохраняет водителя с адресами из списка 1"""
        if not self.selected_markers:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Список 1 пуст")
            return

        # Получаем адреса и их данные
        addresses = list(self.selected_markers.keys())

        # Показываем диалог сохранения
        dialog = DriverSaveDialog(self.root, addresses, self.list1_sum, self.list1_weight)
        driver_name = dialog.wait_for_result()

        if not driver_name:
            return

        # Получаем цвет первого маркера (все должны быть одного цвета)
        first_marker = next(iter(self.selected_markers.values()))
        color = first_marker.marker_color_circle

        # Создаем ключ для сохранения
        import time
        timestamp = int(time.time())
        key = f"{driver_name}_{timestamp}"

        # Сохраняем данные водителя
        self.data_manager.saved_drivers[key] = {
            'driver_name': driver_name,
            'color': color,
            'count': len(addresses),
            'sum': self.list1_sum,
            'addresses': addresses.copy()
        }

        # Добавляем водителя в реестр если его нет
        if driver_name not in self.data_manager.drivers_registry:
            self.data_manager.drivers_registry[driver_name] = {'region': ''}

        # Очищаем список 1
        for address in addresses:
            if address in self.selected_markers:
                marker = self.selected_markers[address]
                # Маркеры остаются с назначенным цветом (не возвращаем в brown)
                del self.selected_markers[address]

        # Отмечаем адреса как обработанные вручную для алгоритма распределения
        if hasattr(self.distribution_algorithm, 'distribution_params') and self.distribution_algorithm.distribution_params:
            self.distribution_algorithm.mark_manually_processed(addresses)
            self.distribution_algorithm.update_available_addresses(
                self.markers, self.selected_markers, self.selected_markers2
            )

        # Сбрасываем кнопку распределения если нужно
        if hasattr(self.distribution_algorithm, 'distribution_params') and self.distribution_algorithm.distribution_params:
            if not self.distribution_algorithm.has_remaining_addresses():
                self.distribute_button.config(text="Распределить")
                self.distribution_algorithm.clear_session_colors()

        self.update_counts()

        ModernMessageBox.show_info(self.root, "Успех",
                                 f"Водитель '{driver_name}' сохранен с {len(addresses)} адресами")

        print(f"💾 Сохранен водитель: {driver_name}, адресов: {len(addresses)}, цвет: {color}")

    def load_driver(self):
        """Загружает сохраненного водителя"""
        if not self.data_manager.saved_drivers:
            ModernMessageBox.show_info(self.root, "Информация", "Нет сохраненных водителей")
            return

        # Создаем простой диалог выбора (пока без сложного UI)
        drivers_list = []
        for key, driver_info in self.data_manager.saved_drivers.items():
            driver_name = driver_info.get('driver_name', key)
            count = driver_info.get('count', 0)
            sum_value = driver_info.get('sum', 0.0)
            drivers_list.append(f"{driver_name} ({count} адресов, сумма: {sum_value:.2f})")

        # Пока используем простое сообщение
        message = "Сохраненные водители:\n\n" + "\n".join(drivers_list[:10])
        if len(drivers_list) > 10:
            message += f"\n... и еще {len(drivers_list) - 10} водителей"

        ModernMessageBox.show_info(self.root, "Сохраненные водители", message)

    def update_tasks_display(self):
        """Обновляет отображение списка задач"""
        # Очищаем список
        for item in self.tasks_tree.get_children():
            self.tasks_tree.delete(item)

        # Сортируем задачи: невыполненные сверху, выполненные снизу
        sorted_tasks = sorted(
            self.data_manager.tasks.items(),
            key=lambda x: (x[1].get('completed', False), x[0])
        )

        # Добавляем задачи в список
        for task_id, task_info in sorted_tasks:
            completed_text = "Да" if task_info.get('completed', False) else "Нет"

            self.tasks_tree.insert('', 'end', values=(
                task_id,
                task_info.get('date', ''),
                task_info.get('task', ''),
                task_info.get('driver', ''),
                completed_text
            ))

    def add_task(self):
        """Добавляет новую задачу"""
        dialog = TaskDialog(self.root)
        task_data = dialog.wait_for_result()

        if task_data:
            # Создаем новую задачу
            task_id = self.data_manager.next_task_id
            self.data_manager.tasks[task_id] = task_data
            self.data_manager.next_task_id += 1

            # Обновляем отображение
            self.update_tasks_display()

            ModernMessageBox.show_info(self.root, "Успех", "Задача создана")
            print(f"📝 Создана задача ID {task_id}: {task_data['task']}")

    def edit_task(self):
        """Редактирует выбранную задачу"""
        selected = self.tasks_tree.selection()
        if not selected:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Выберите задачу для редактирования")
            return

        # Получаем ID задачи
        item = selected[0]
        task_id = int(self.tasks_tree.item(item, 'values')[0])

        if task_id not in self.data_manager.tasks:
            ModernMessageBox.show_error(self.root, "Ошибка", "Задача не найдена")
            return

        # Показываем диалог редактирования
        dialog = TaskDialog(self.root, self.data_manager.tasks[task_id])
        task_data = dialog.wait_for_result()

        if task_data:
            # Обновляем задачу
            self.data_manager.tasks[task_id] = task_data

            # Обновляем отображение
            self.update_tasks_display()

            ModernMessageBox.show_info(self.root, "Успех", "Задача обновлена")
            print(f"✏️ Обновлена задача ID {task_id}: {task_data['task']}")

    def delete_task(self):
        """Удаляет выбранную задачу"""
        selected = self.tasks_tree.selection()
        if not selected:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Выберите задачу для удаления")
            return

        # Получаем ID задачи
        item = selected[0]
        task_id = int(self.tasks_tree.item(item, 'values')[0])
        task_name = self.tasks_tree.item(item, 'values')[2]

        # Подтверждение удаления
        from tkinter import messagebox
        if messagebox.askyesno("Подтверждение", f"Удалить задачу '{task_name}'?"):
            if task_id in self.data_manager.tasks:
                del self.data_manager.tasks[task_id]

                # Обновляем отображение
                self.update_tasks_display()

                ModernMessageBox.show_info(self.root, "Успех", "Задача удалена")
                print(f"🗑️ Удалена задача ID {task_id}: {task_name}")

    def mark_task_completed(self):
        """Отмечает задачу как выполненную"""
        selected = self.tasks_tree.selection()
        if not selected:
            ModernMessageBox.show_warning(self.root, "Предупреждение", "Выберите задачу")
            return

        # Получаем ID задачи
        item = selected[0]
        task_id = int(self.tasks_tree.item(item, 'values')[0])

        if task_id in self.data_manager.tasks:
            # Переключаем статус выполнения
            current_status = self.data_manager.tasks[task_id].get('completed', False)
            self.data_manager.tasks[task_id]['completed'] = not current_status

            # Обновляем отображение
            self.update_tasks_display()

            status_text = "выполненной" if not current_status else "невыполненной"
            ModernMessageBox.show_info(self.root, "Успех", f"Задача отмечена как {status_text}")
            print(f"✅ Задача ID {task_id} отмечена как {status_text}")
    def add_schedule_region(self): pass
    def edit_schedule_entry(self): pass
    def delete_schedule_entry(self): pass

    def on_closing(self):
        """Обработчик закрытия приложения"""
        self.data_manager.save_all_data()
        self.root.destroy()


if __name__ == "__main__":
    app = AddressManager()
    app.root.mainloop()
