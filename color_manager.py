"""
Модуль управления цветами маркеров
Содержит логику назначения и отслеживания цветов для маркеров
"""

from typing import Set, List, Dict, Any, Optional


class ColorManager:
    """Класс для управления цветами маркеров"""
    
    def __init__(self):
        # Доступные цвета для маркеров (исключая blue, green, brown, purple которые зарезервированы)
        # Расширенная палитра из 20 цветов для поддержки большого количества маршрутов
        # red перемещен в конец списка, чтобы избежать частого назначения
        self.available_colors = [
            "orange", "pink", "black", "gray", "cyan", "magenta", "yellow", "darkgreen",
            "navy", "maroon", "olive", "teal", "silver", "lime", "aqua", "fuchsia",
            "indigo", "crimson", "gold", "coral", "red"
        ]
        
        # Список используемых цветов для маркеров
        self.used_colors = []
    
    def get_next_available_color(self, saved_drivers: Dict = None, 
                               current_session_colors: Set = None,
                               all_markers: Dict = None,
                               selected_markers: Dict = None,
                               selected_markers2: Dict = None,
                               map_widget = None) -> str:
        """Возвращает следующий доступный цвет для маркеров"""
        print(f"\n🎨 === ВЫБОР ЦВЕТА ===")
        
        # Собираем все используемые цвета из разных источников
        all_used_colors = set()
        
        # 1. Добавляем зарезервированные цвета
        reserved_colors = {"blue", "green", "brown", "purple"}
        all_used_colors.update(reserved_colors)
        print(f"🔒 Зарезервированные цвета: {sorted(reserved_colors)}")
        
        # 2. Добавляем цвета текущей сессии распределения (НЕ из расписания)
        if current_session_colors:
            all_used_colors.update(current_session_colors)
            print(f"📝 Цвета текущей сессии: {sorted(current_session_colors)}")
        
        # 3. Добавляем цвета из сохраненных водителей в расписании
        saved_colors = set()
        if saved_drivers:
            for key, driver_info in saved_drivers.items():
                if 'color' in driver_info and driver_info['color']:
                    saved_colors.add(driver_info['color'])
        all_used_colors.update(saved_colors)
        print(f"💾 Цвета из сохраненных водителей: {sorted(saved_colors)}")
        
        # 4. Добавляем цвета из ВСЕХ маркеров на карте (включая сохраненные с водителями)
        marker_colors = set()
        if all_markers:
            for address, marker in all_markers.items():
                try:
                    # Пытаемся получить цвет маркера разными способами
                    if hasattr(marker, 'marker_color_circle') and marker.marker_color_circle:
                        marker_colors.add(marker.marker_color_circle)
                        print(f"    🎨 Маркер {address}: цвет {marker.marker_color_circle}")
                    elif hasattr(marker, 'color') and marker.color:
                        marker_colors.add(marker.color)
                        print(f"    🎨 Маркер {address}: цвет {marker.color}")
                except Exception as e:
                    print(f"    ❌ Ошибка получения цвета маркера {address}: {e}")
        all_used_colors.update(marker_colors)
        print(f"🗺️ Цвета из маркеров на карте: {sorted(marker_colors)}")
        
        # 4.1. Дополнительная проверка через canvas_marker_list карты
        canvas_marker_colors = set()
        if map_widget and hasattr(map_widget, 'canvas_marker_list'):
            for canvas_marker in map_widget.canvas_marker_list:
                try:
                    if hasattr(canvas_marker, 'marker_color_circle') and canvas_marker.marker_color_circle:
                        canvas_marker_colors.add(canvas_marker.marker_color_circle)
                        print(f"    🖼️ Canvas маркер: цвет {canvas_marker.marker_color_circle}")
                except Exception as e:
                    print(f"    ❌ Ошибка получения цвета canvas маркера: {e}")
        all_used_colors.update(canvas_marker_colors)
        print(f"🖼️ Цвета из canvas маркеров: {sorted(canvas_marker_colors)}")
        
        # 5. Добавляем цвета из выбранных маркеров (список 1)
        selected_colors = set()
        if selected_markers:
            for address, marker in selected_markers.items():
                try:
                    if hasattr(marker, 'marker_color_circle'):
                        selected_colors.add(marker.marker_color_circle)
                    elif hasattr(marker, 'color'):
                        selected_colors.add(marker.color)
                except:
                    pass
        all_used_colors.update(selected_colors)
        print(f"✅ Цвета из выбранных маркеров: {sorted(selected_colors)}")
        
        # 6. Добавляем цвета из списка 2
        list2_colors = set()
        if selected_markers2:
            for address, marker in selected_markers2.items():
                try:
                    if hasattr(marker, 'marker_color_circle'):
                        list2_colors.add(marker.marker_color_circle)
                    elif hasattr(marker, 'color'):
                        list2_colors.add(marker.color)
                except:
                    pass
        all_used_colors.update(list2_colors)
        print(f"📋 Цвета из списка 2: {sorted(list2_colors)}")
        
        print(f"🎨 ВСЕ используемые цвета: {sorted(all_used_colors)}")
        print(f"🌈 Доступные цвета: {self.available_colors}")
        
        # Ищем первый доступный цвет
        for color in self.available_colors:
            if color not in all_used_colors:
                # НЕ добавляем в used_colors здесь, это будет сделано при фактическом использовании
                print(f"✅ ВЫБРАН новый цвет: {color}")
                print(f"🎨 === КОНЕЦ ВЫБОРА ЦВЕТА ===\n")
                return color
        
        # Если все цвета использованы, подсчитываем частоту использования
        print(f"⚠️ Все цвета использованы, подсчитываем частоту...")
        color_usage = {}
        
        # Инициализируем счетчики
        for color in self.available_colors:
            color_usage[color] = 0
        
        # Подсчитываем использование каждого цвета (исключая зарезервированные)
        for color in all_used_colors:
            if color in self.available_colors and color not in reserved_colors:
                color_usage[color] += 1
        
        print(f"📊 Частота использования цветов: {color_usage}")
        
        # Находим цвет с минимальным использованием (исключая зарезервированные)
        available_for_reuse = {k: v for k, v in color_usage.items() if k not in reserved_colors}
        if available_for_reuse:
            min_usage_color = min(available_for_reuse.items(), key=lambda x: x[1])[0]
            print(f"⚠️ ПОВТОРНО назначен наименее используемый цвет: {min_usage_color} (использован {available_for_reuse[min_usage_color]} раз)")
        else:
            # Крайний случай - берем первый доступный
            min_usage_color = self.available_colors[0]
            print(f"🚨 КРАЙНИЙ СЛУЧАЙ: назначен первый доступный цвет: {min_usage_color}")
        
        print(f"🎨 === КОНЕЦ ВЫБОРА ЦВЕТА ===\n")
        return min_usage_color
    
    def initialize_used_colors(self, saved_drivers: Dict) -> None:
        """Инициализирует список используемых цветов на основе сохраненных водителей"""
        self.used_colors = []
        
        # Добавляем цвета из сохраненных водителей в расписании
        if saved_drivers:
            for key, driver_info in saved_drivers.items():
                if 'color' in driver_info and driver_info['color']:
                    color = driver_info['color']
                    if color not in self.used_colors:
                        self.used_colors.append(color)
                        driver_name = driver_info.get('driver_name', key)
                        print(f"Инициализирован используемый цвет: {color} для водителя {driver_name}")
        
        print(f"Инициализировано {len(self.used_colors)} используемых цветов: {self.used_colors}")
