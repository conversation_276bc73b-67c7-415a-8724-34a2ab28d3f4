import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from tkintermapview import TkinterMapView
import pandas as pd
import os
import googlemaps
import time
from datetime import datetime
import requests
import json

class YandexGeocoder:
    """Класс для работы с Яндекс Граеокодером"""
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://geocode-maps.yandex.ru/1.x/"

    def geocode(self, address):
        """Геокодирование адреса через Яндекс API"""
        params = {
            "apikey": self.api_key,
            "geocode": address,
            "format": "json",
            "lang": "ru_RU"
        }
        try:
            response = requests.get(self.base_url, params=params).json()
            features = response["response"]["GeoObjectCollection"]["featureMember"]
            if not features:
                return None, None

            # Берем первый результат
            pos = features[0]["GeoObject"]["Point"]["pos"]
            lon, lat = map(float, pos.split())
            return lat, lon
        except Exception as e:
            print(f"Ошибка Яндекс Геокодера: {e}")
            return None, None

    def suggest_similar(self, query):
        """Поиск похожих адресов через API Геоседжеста"""
        url = "https://suggest-maps.yandex.ru/v1/suggest"
        params = {
            "apikey": self.api_key,
            "text": query,
            "type": "geo",
            "lang": "ru_RU"
        }
        try:
            response = requests.get(url, params=params).json()
            return [item["display_name"] for item in response.get("results", [])[:5]]  # первые 5 вариантов
        except Exception as e:
            print(f"Ошибка подсказок Яндекс: {e}")
            return []

class MapApp:
    def __init__(self, root):

        self.root = root
        self.root.title("Карта с загрузкой адресов")
        self.root.geometry("1400x800")

        # Настраиваем стиль заголовка окна и общие стили
        self.style = ttk.Style()

        # Настройка цветов для темной темы
        self.style.configure(".",
                       background="#2b3e50",
                       foreground="white",
                       fieldbackground="#34495e")

        # Настройка стиля для LabelFrame (рамки с заголовками)
        self.style.configure("TLabelframe",
                       background="#2b3e50",
                       foreground="#ecf0f1",
                       bordercolor="#34495e")

        self.style.configure("TLabelframe.Label",
                       background="#2b3e50",
                       foreground="#ecf0f1",
                       font=("Segoe UI", 9, "bold"))

        # Настройка стиля для Notebook (вкладки)
        self.style.configure("TNotebook",
                       background="#2b3e50",
                       bordercolor="#34495e")

        self.style.configure("TNotebook.Tab",
                       background="#34495e",
                       foreground="#ecf0f1",
                       padding=[10, 5])

        self.style.map("TNotebook.Tab",
                 background=[("selected", "#2b3e50"),
                           ("active", "#3d566e")])

        # Настройка стиля для кнопок
        self.style.configure("TButton",
                       background="#34495e",
                       foreground="white",
                       bordercolor="#2c3e50",
                       focuscolor="none")

        self.style.map("TButton",
                 background=[("active", "#3d566e"),
                           ("pressed", "#2c3e50")])

        # Настройка стиля для полей ввода
        self.style.configure("TEntry",
                       fieldbackground="#34495e",
                       foreground="white",
                       bordercolor="#2c3e50",
                       insertcolor="white")

        # Настройка стиля для Combobox
        self.style.configure("TCombobox",
                       fieldbackground="#34495e",
                       foreground="white",
                       bordercolor="#2c3e50",
                       arrowcolor="white")

        # Настройка стиля для Treeview
        self.style.configure("Treeview",
                       background="#34495e",
                       foreground="white",
                       fieldbackground="#34495e")

        self.style.configure("Treeview.Heading",
                       background="#2c3e50",
                       foreground="#ecf0f1",
                       font=("Segoe UI", 9, "bold"))

        # Настройка стиля для скроллбаров
        self.style.configure("Vertical.TScrollbar",
                       background="#34495e",
                       troughcolor="#2b3e50",
                       bordercolor="#34495e",
                       arrowcolor="#ecf0f1")

        # Инициализация переменной для выбора типа карты
        self.map_type = tk.StringVar(value="normal")

        # Закрытый API ключ (замените на ваш реальный ключ)
        self.GOOGLE_MAPS_API_KEY = "AIzaSyBikxnCmNgmoerGPe0UOk5NukKfyd7EmC0"
        self.YANDEX_MAPS_API_KEY = "e1bf17bc-d8ae-42af-a034-643d092fe2ce"  # Добавьте это

        # Основной контейнер с вертикальным разделением (без левого фрейма)
        self.right_paned = ttk.PanedWindow(root, orient=tk.VERTICAL)
        self.right_paned.pack(fill=tk.BOTH, expand=True)

        # Верхний правый фрейм (карта и управление)
        self.top_right_frame = ttk.Frame(self.right_paned)
        self.right_paned.add(self.top_right_frame, weight=1)

        # Нижний правый фрейм (списки адресов)
        self.bottom_right_frame = ttk.Frame(self.right_paned)
        self.right_paned.add(self.bottom_right_frame, weight=1)

        # Карта
        self.map_widget = TkinterMapView(self.top_right_frame, width=700, height=400)
        self.map_widget.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.map_widget.set_position(55.7558, 37.6176)  # Москва по умолчанию
        self.map_widget.set_zoom(12)

        # Панель загрузки файлов (слева от карты)
        self.file_frame = ttk.Frame(self.top_right_frame, width=200)
        self.file_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=5, pady=5)

        # Контейнер для списков адресов
        self.lists_paned = ttk.PanedWindow(self.bottom_right_frame, orient=tk.HORIZONTAL)
        self.lists_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.selected_lists_paned = ttk.PanedWindow(self.lists_paned, orient=tk.VERTICAL)
        self.lists_paned.add(self.selected_lists_paned, weight=1)

        # Фрейм для всех адресов с фильтрацией
        self.all_addresses_container = ttk.Frame(self.lists_paned)
        self.lists_paned.add(self.all_addresses_container, weight=2)

        # Фрейм для списка водителей (справа от списка адресов)
        self.drivers_list_container = ttk.Frame(self.lists_paned)
        self.lists_paned.add(self.drivers_list_container, weight=1)

        # Заголовок и поле для добавления водителя
        self.drivers_list_frame = ttk.LabelFrame(self.drivers_list_container, text="Водители (0)")
        self.drivers_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Поле для ввода нового водителя
        self.driver_entry_frame = ttk.Frame(self.drivers_list_frame)
        self.driver_entry_frame.pack(fill=tk.X, pady=5)

        self.driver_entry = ttk.Entry(self.driver_entry_frame)
        self.driver_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.driver_entry.bind("<Return>", self.add_driver_to_registry)

        ttk.Button(
            self.driver_entry_frame,
            text="+",
            width=3,
            command=self.add_driver_to_registry
        ).pack(side=tk.RIGHT, padx=5)

        # Поле фильтрации по региону
        self.driver_filter_frame = ttk.Frame(self.drivers_list_frame)
        self.driver_filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.driver_filter_frame, text="Фильтр по региону:").pack(side=tk.LEFT, padx=5)
        self.driver_filter_var = tk.StringVar()
        self.driver_filter_entry = ttk.Entry(self.driver_filter_frame, textvariable=self.driver_filter_var)
        self.driver_filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.driver_filter_var.trace("w", lambda *args: self.apply_driver_filter())

        ttk.Button(
            self.driver_filter_frame,
            text="×",
            width=3,
            command=self.reset_driver_filter
        ).pack(side=tk.RIGHT, padx=5)

        # Список водителей (Treeview)
        self.drivers_list_frame2 = ttk.Frame(self.drivers_list_frame)
        self.drivers_list_frame2.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Настраиваем стиль для списка водителей
        self.style.configure("Drivers.Treeview",
                       background="#2b3e50",  # Темно-синий фон
                       foreground="white",    # Белый текст
                       fieldbackground="#2b3e50",  # Фон полей
                       font=("Segoe UI", 10),  # Увеличенный шрифт
                       rowheight=25)  # Увеличенная высота строки

        self.style.configure("Drivers.Treeview.Heading",
                       background="#34495e",  # Менее яркий серо-синий для заголовков
                       foreground="#ecf0f1",  # Светло-серый текст заголовков
                       font=("Segoe UI", 9, "bold"))  # Жирный шрифт для заголовков

        # Создаем Treeview для отображения водителей в виде таблицы
        self.drivers_registry_list = ttk.Treeview(
            self.drivers_list_frame2,
            columns=("driver", "region"),
            show="headings",
            style="Drivers.Treeview"
        )

        # Настраиваем заголовки столбцов
        self.drivers_registry_list.heading("driver", text="Водитель")
        self.drivers_registry_list.heading("region", text="Регион")

        # Настраиваем ширину столбцов
        self.drivers_registry_list.column("driver", width=100)  # Уменьшено с 150 до 100
        self.drivers_registry_list.column("region", width=150)

        # Добавляем прокрутку
        self.drivers_registry_scroll = ttk.Scrollbar(self.drivers_list_frame2, orient="vertical", command=self.drivers_registry_list.yview)
        self.drivers_registry_list.configure(yscrollcommand=self.drivers_registry_scroll.set)

        # Размещаем список и прокрутку
        self.drivers_registry_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.drivers_registry_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Привязываем двойной клик для редактирования водителя
        self.drivers_registry_list.bind("<Double-1>", self.edit_driver)

        # Кнопка удаления водителя
        ttk.Button(
            self.drivers_list_frame,
            text="Удалить выбранного",
            command=self.delete_driver_from_registry
        ).pack(fill=tk.X, padx=5, pady=5)

        # Контекстное меню для списка водителей
        self.drivers_registry_menu = tk.Menu(self.root, tearoff=0)
        self.drivers_registry_menu.add_command(label="Редактировать", command=self.edit_driver)
        self.drivers_registry_menu.add_command(label="Удалить", command=self.delete_driver_from_registry)
        self.drivers_registry_menu.add_command(label="Копировать", command=lambda: self.copy_from_registry())

        self.drivers_registry_list.bind("<Button-3>", lambda e: self.show_drivers_registry_menu(e))

        # Поле фильтрации
        self.filter_frame = ttk.Frame(self.all_addresses_container)
        self.filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.filter_frame, text="Фильтр:").pack(side=tk.LEFT)
        self.filter_var = tk.StringVar()
        self.filter_entry = ttk.Entry(self.filter_frame, textvariable=self.filter_var)
        self.filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.filter_var.trace("w", lambda *args: self.apply_filter())

        ttk.Button(
            self.filter_frame,
            text="×",
            width=3,
            command=self.reset_filter
        ).pack(side=tk.LEFT, padx=5)

        # Кнопки загрузки Excel справа от фильтра
        ttk.Button(
            self.filter_frame,
            text="Загрузить Excel",
            command=self.load_excel,
            width=15
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            self.filter_frame,
            text="Добавить из Excel",
            command=self.load_additional_excel,
            width=20  # Увеличиваем ширину с 15 до 20
        ).pack(side=tk.LEFT, padx=5)

        # Основной список адресов
        self.all_addresses_frame = ttk.LabelFrame(self.all_addresses_container, text="Все адреса")
        self.all_addresses_frame.pack(fill=tk.BOTH, expand=True)

        # Создаем фрейм для списка адресов
        self.all_addresses_list_frame = ttk.Frame(self.all_addresses_frame)
        self.all_addresses_list_frame.pack(fill=tk.BOTH, expand=True)

        # Настраиваем стиль для списка адресов
        self.style.configure("Addresses.Treeview",
                       background="#2b3e50",  # Темно-синий фон
                       foreground="white",    # Белый текст
                       fieldbackground="#2b3e50",  # Фон полей
                       font=("Segoe UI", 10),  # Увеличенный шрифт
                       rowheight=25)  # Увеличенная высота строки

        self.style.configure("Addresses.Treeview.Heading",
                       background="#34495e",  # Менее яркий серо-синий для заголовков
                       foreground="#ecf0f1",  # Светло-серый текст заголовков
                       font=("Segoe UI", 9, "bold"))  # Жирный шрифт для заголовков

        # Создаем Treeview для отображения адресов с суммой и весом
        self.all_addresses_list = ttk.Treeview(
            self.all_addresses_list_frame,
            columns=("address", "sum", "weight"),
            show="headings",
            selectmode="extended",
            style="Addresses.Treeview"
        )

        # Настраиваем заголовки столбцов
        self.all_addresses_list.heading("address", text="Адрес")
        self.all_addresses_list.heading("sum", text="Сумма")
        self.all_addresses_list.heading("weight", text="Вес")

        # Настраиваем ширину столбцов
        self.all_addresses_list.column("address", width=250)
        self.all_addresses_list.column("sum", width=70, anchor=tk.E)
        self.all_addresses_list.column("weight", width=70, anchor=tk.E)

        # Добавляем прокрутку
        self.all_addresses_scroll = ttk.Scrollbar(self.all_addresses_list_frame, orient="vertical", command=self.all_addresses_list.yview)
        self.all_addresses_list.configure(yscrollcommand=self.all_addresses_scroll.set)

        # Размещаем список и прокрутку
        self.all_addresses_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.all_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Список избранных адресов
        self.selected_addresses_frame = ttk.LabelFrame(self.selected_lists_paned, text="Список 1 (0)")
        self.selected_lists_paned.add(self.selected_addresses_frame, weight=1)

        # Выбор водителя
        self.selector_frame = ttk.Frame(self.selected_lists_paned)
        self.selected_lists_paned.add(self.selector_frame)

        self.list_selector = tk.StringVar(value="list1")
        ttk.Radiobutton(
            self.selector_frame,
            text="Список 1",
            variable=self.list_selector,
            value="list1"
        ).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(
            self.selector_frame,
            text="Список 2",
            variable=self.list_selector,
            value="list2"
        ).pack(side=tk.LEFT, padx=5)

        self.driver_frame = ttk.Frame(self.selected_addresses_frame)
        self.driver_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.driver_frame, text="Водитель:").pack(side=tk.LEFT)
        self.driver_var = tk.StringVar()
        self.driver_combobox = ttk.Combobox(
            self.driver_frame,
            textvariable=self.driver_var,
            values=["Иванов", "Петров", "Сидоров", "Смирнов"],
            state="normal"
        )
        self.driver_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Кнопка "Сохранить избранное" перемещена сюда
        ttk.Button(
            self.driver_frame,
            text="Сохранить",
            command=self.save_selected_with_driver
        ).pack(side=tk.RIGHT, padx=5)

        # Кнопка для автоматического распределения адресов
        ttk.Button(
            self.driver_frame,
            text="Распределить",
            command=self.show_distribution_settings
        ).pack(side=tk.RIGHT, padx=5)

        self.selected_addresses_list = tk.Listbox(self.selected_addresses_frame)
        self.selected_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.selected_addresses_scroll = ttk.Scrollbar(self.selected_addresses_list)
        self.selected_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.selected_addresses_list.config(yscrollcommand=self.selected_addresses_scroll.set)
        self.selected_addresses_scroll.config(command=self.selected_addresses_list.yview)

        # Вставляем после всего кода первого списка (примерно строка 150):
        self.selected_addresses_frame2 = ttk.LabelFrame(self.selected_lists_paned, text="Список 2 (0)")
        self.selected_lists_paned.add(self.selected_addresses_frame2, weight=1)

        self.selected_addresses_list2 = tk.Listbox(self.selected_addresses_frame2)
        self.selected_addresses_list2.pack(fill=tk.BOTH, expand=True)
        self.selected_addresses_scroll2 = ttk.Scrollbar(self.selected_addresses_list2)
        self.selected_addresses_scroll2.pack(side=tk.RIGHT, fill=tk.Y)
        self.selected_addresses_list2.config(yscrollcommand=self.selected_addresses_scroll2.set)
        self.selected_addresses_scroll2.config(command=self.selected_addresses_list2.yview)
        self.selected_addresses_list2.bind("<Double-Button-1>", self.return_to_all_from_list2)



        # Фрейм "Сохраненные водители" удален, так как его функциональность перенесена в список с расписанием


        # Добавляем обработчики горячих клавиш
        self.root.bind('<Control-c>', self.copy_selected)
        self.root.bind('<Control-v>', self.paste_to_entry)

        # Создаем контекстное меню
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Копировать", command=self.copy_selected)
        self.context_menu.add_command(label="Вставить", command=self.paste_to_entry)

        # Создаем отдельное контекстное меню для общего списка адресов
        self.all_addresses_menu = tk.Menu(self.root, tearoff=0)
        self.all_addresses_menu.add_command(label="Копировать", command=self.copy_selected)
        self.all_addresses_menu.add_command(label="Добавить в список", command=self.add_to_selected)
        self.all_addresses_menu.add_separator()
        self.all_addresses_menu.add_command(label="Удалить выбранные", command=self.delete_selected_from_all)



        # Буфер обмена
        self.clipboard = ""

        # Кнопка добавления только адреса перемещена в метод setup_left_panel

        # Хранилище данных
        self.markers = {}
        self.selected_markers = {}
        self.selected_markers2 = {}
        self.original_markers = {}
        self.failed_addresses = []
        self.deleted_addresses = []  # Новый список для хранения удаленных адресов

        # Хранилище для расписания водителей
        # Изменяем структуру: теперь для каждого дня хранится список записей [{'region': '...', 'driver': '...'}]
        self.drivers_schedule = {
            'Понедельник': [
                {'region': 'Витебск, Городок, Лиозно, Сенно', 'driver': 'Тишковец'},
                {'region': 'Жлобин, Светлогорск', 'driver': 'Серченя'},
                {'region': 'Гомель, Буда-Кошелево, Чечерск, Ветка', 'driver': 'Батый'},
                {'region': 'Слуцк, Копыль', 'driver': 'Синяк'},
                {'region': 'Солигорск, Любань, Старобин', 'driver': 'Егоров'},
                {'region': 'Дзержинск, Фаниполь', 'driver': 'Грек'},
                {'region': 'Барановичи', 'driver': 'Брицкий'},
                {'region': 'Бобруйск, Глуск', 'driver': 'Кирбай'},
                {'region': 'Борисов, Жодино, Смолевичи', 'driver': 'Егоров'},
                {'region': 'Минск', 'driver': ''}
            ],
            'Вторник': [],
            'Среда': [],
            'Четверг': [],
            'Пятница': []
        }

        # Настройка элементов управления
        self.setup_left_panel()

        # Хранилище для значений суммы и веса адресов
        self.address_values = {}  # Формат: {address: {'sum': float, 'weight': float}}

        # Общие значения суммы и веса для списков
        self.list1_sum = 0.0
        self.list1_weight = 0.0
        self.list2_sum = 0.0
        self.list2_weight = 0.0

        # Хранилище для сохраненных водителей и их адресов
        self.saved_drivers = {}  # Формат: {driver_name: {'addresses': [...], 'color': '...', 'count': N}}

        # Хранилище для реестра водителей
        self.drivers_registry = {}  # Формат: {driver_name: {'region': '...'}}

        # Хранилище для адресов и контрагентов из задач
        self.task_addresses_registry = []  # Формат: [{'address': '...', 'contractor': '...'}]

        # Хранилище для задач
        self.tasks = {}  # Формат: {task_id: {'date': '...', 'task': '...', 'driver': '...', 'description': '...', 'completed': False}}
        self.next_task_id = 1  # Счетчик для генерации уникальных ID задач

        # Список используемых цветов для маркеров
        self.used_colors = []
        # Доступные цвета для маркеров (исключая blue, green, brown, purple которые зарезервированы)
        # Расширенная палитра из 20 цветов для поддержки большого количества маршрутов
        self.available_colors = [
            "red", "orange", "pink", "black", "gray", "cyan", "magenta", "yellow", "darkgreen",
            "navy", "maroon", "olive", "teal", "silver", "lime", "aqua", "fuchsia",
            "indigo", "crimson", "gold", "coral"
        ]

        # Кэш геокодированных адресов
        self.geocode_cache = {}

        # Загружаем кэш геокодированных адресов
        self.load_geocode_cache()

        # Загружаем реестр водителей
        self.load_drivers_registry()

        # Загружаем реестр адресов и контрагентов
        self.load_task_addresses_registry()

        # Загружаем список задач
        self.load_tasks_list()

        # Загружаем расписание водителей
        self.load_schedule()

        # Загружаем адреса водителей
        self.load_drivers_addresses()

        # Загружаем значения суммы и веса для адресов
        self.load_address_values()

        # Обновляем отображение расписания
        self.update_schedule_display()

        # Инициализируем используемые цвета после загрузки всех данных
        self.initialize_used_colors()

        # Инициализация геокодера
        # self.init_geocoder()
        self.init_geocoders()

        # Привязываем событие закрытия окна для сохранения кэша
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Привязка событий
        self.all_addresses_list.bind("<Double-Button-1>", self.add_to_selected)
        self.selected_addresses_list.bind("<Double-Button-1>", self.return_to_all)
        # Привязываем контекстное меню к спискам и полю ввода
        self.all_addresses_list.bind("<Button-3>", self.show_all_addresses_context_menu)
        self.selected_addresses_list.bind("<Button-3>", self.show_context_menu)
        self.failed_addresses_list.bind("<Button-3>", self.show_context_menu)
        self.address_entry.bind("<Button-3>", self.show_context_menu)

        # Контекстное меню для списка удаленных
        self.deleted_menu = tk.Menu(self.root, tearoff=0)
        self.deleted_menu.add_command(label="Восстановить", command=self.restore_deleted_address)
        self.deleted_menu.add_command(label="Копировать", command=lambda: self.copy_from_list(self.deleted_addresses_list))

        self.deleted_addresses_list.bind("<Button-3>", lambda e: self.show_deleted_context_menu(e))

        # Контекстное меню для списка водителей удалено, так как его функциональность перенесена в список с расписанием

        # Контекстное меню для списка задач
        self.tasks_menu = tk.Menu(self.root, tearoff=0)
        self.tasks_menu.add_command(label="Редактировать", command=self.edit_task)
        self.tasks_menu.add_command(label="Удалить", command=self.delete_task)

        self.tasks_list.bind("<Button-3>", lambda e: self.show_tasks_context_menu(e))

    def setup_left_panel(self):
        """Настройка элементов управления в панели задач"""
        # Фрейм для списка задач с современным стилем
        self.tasks_frame = ttk.LabelFrame(
            self.file_frame,
            text="📋 Управление задачами (0)",
            style="info.TLabelframe"
        )
        self.tasks_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Создаем вкладки
        self.tasks_notebook = ttk.Notebook(self.tasks_frame)
        self.tasks_notebook.pack(fill=tk.BOTH, expand=True)

        # Создаем первую вкладку для списка задач
        self.tasks_tab = ttk.Frame(self.tasks_notebook)
        self.tasks_notebook.add(self.tasks_tab, text="Задачи")

        # Создаем вторую вкладку для расписания водителей
        self.info_tab = ttk.Frame(self.tasks_notebook)
        self.tasks_notebook.add(self.info_tab, text="Расписание")

        # Создаем третью вкладку для служебных списков
        self.service_tab = ttk.Frame(self.tasks_notebook)
        self.tasks_notebook.add(self.service_tab, text="Служебные")

        # Создаем фрейм для выбора дня недели
        day_select_frame = ttk.Frame(self.info_tab)
        day_select_frame.pack(fill=tk.X, padx=10, pady=5)

        # Метка для выбора дня недели
        ttk.Label(day_select_frame, text="День недели:").pack(side=tk.LEFT, padx=5)

        # Выпадающий список для выбора дня недели
        self.day_var = tk.StringVar(value="Понедельник")
        days = ["Понедельник", "Вторник", "Среда", "Четверг", "Пятница"]
        day_combobox = ttk.Combobox(day_select_frame, textvariable=self.day_var, values=days, state="readonly")
        day_combobox.pack(side=tk.LEFT, padx=5)
        day_combobox.bind("<<ComboboxSelected>>", self.update_schedule_display)

        # Кнопка для редактирования расписания
        ttk.Button(day_select_frame, text="Редактировать", command=self.edit_schedule).pack(side=tk.RIGHT, padx=5)

        # Создаем фрейм для таблицы расписания
        schedule_frame = ttk.Frame(self.info_tab)
        schedule_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Настраиваем стиль для таблицы расписания
        self.style.configure("Schedule.Treeview",
                       background="#2b3e50",  # Темно-синий фон
                       foreground="white",    # Белый текст
                       fieldbackground="#2b3e50",  # Фон полей
                       font=("Segoe UI", 10),  # Увеличенный шрифт
                       rowheight=25)  # Увеличенная высота строки

        self.style.configure("Schedule.Treeview.Heading",
                       background="#34495e",  # Менее яркий серо-синий для заголовков
                       foreground="#ecf0f1",  # Светло-серый текст заголовков
                       font=("Segoe UI", 9, "bold"))  # Жирный шрифт для заголовков

        # Создаем таблицу для отображения расписания
        columns = ("region", "driver", "color", "points_count", "sum")
        self.schedule_tree = ttk.Treeview(schedule_frame, columns=columns, show="headings", style="Schedule.Treeview")
        self.schedule_tree.heading("region", text="Регион")
        self.schedule_tree.heading("driver", text="Водитель (закрепл.)")
        self.schedule_tree.heading("color", text="Цвет")
        self.schedule_tree.heading("points_count", text="Кол-во точек")
        self.schedule_tree.heading("sum", text="Сумма")
        self.schedule_tree.column("region", width=250)
        self.schedule_tree.column("driver", width=150)
        self.schedule_tree.column("color", width=80)
        self.schedule_tree.column("points_count", width=80)
        self.schedule_tree.column("sum", width=100)

        # Добавляем полосу прокрутки
        schedule_scroll = ttk.Scrollbar(schedule_frame, orient="vertical", command=self.schedule_tree.yview)
        self.schedule_tree.configure(yscrollcommand=schedule_scroll.set)

        # Размещаем таблицу и полосу прокрутки
        self.schedule_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        schedule_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Привязываем двойной клик для редактирования записи
        self.schedule_tree.bind("<Double-1>", self.edit_schedule_entry)

        # Создаем контекстное меню для таблицы расписания
        self.schedule_menu = tk.Menu(self.root, tearoff=0)
        self.schedule_menu.add_command(label="Редактировать", command=self.edit_schedule_entry)
        self.schedule_menu.add_separator()
        self.schedule_menu.add_command(label="Показать адреса на карте", command=self.show_driver_addresses_on_map)
        self.schedule_menu.add_command(label="Скрыть адреса с карты", command=self.hide_driver_addresses_from_map)
        self.schedule_menu.add_command(label="Загрузить адреса для редактирования", command=self.load_driver_addresses_for_editing)
        self.schedule_menu.add_separator()
        self.schedule_menu.add_command(label="Очистить данные записи", command=self.clear_schedule_entry_data)
        self.schedule_menu.add_separator()
        self.schedule_menu.add_command(label="Назначить на задачу", command=self.assign_driver_to_task)

        # Привязываем контекстное меню к таблице
        self.schedule_tree.bind("<Button-3>", self.show_schedule_context_menu)

        # Настройка вкладки "Служебные"
        # Создаем горизонтальный контейнер для разделения списков и ручного ввода
        self.service_main_paned = ttk.PanedWindow(self.service_tab, orient=tk.HORIZONTAL)
        self.service_main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Создаем контейнер для списков в служебной вкладке (левая часть)
        self.service_lists_paned = ttk.PanedWindow(self.service_main_paned, orient=tk.VERTICAL)
        self.service_main_paned.add(self.service_lists_paned, weight=2)

        # Список необработанных адресов
        self.failed_addresses_frame = ttk.LabelFrame(self.service_lists_paned, text="Необработанные адреса")
        self.service_lists_paned.add(self.failed_addresses_frame, weight=1)

        self.failed_addresses_btn_frame = ttk.Frame(self.failed_addresses_frame)
        self.failed_addresses_btn_frame.pack(fill=tk.X)

        ttk.Button(
            self.failed_addresses_btn_frame,
            text="Удалить выделенное",
            command=self.delete_selected_failed
        ).pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.failed_addresses_list = tk.Listbox(self.failed_addresses_frame)
        self.failed_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.failed_addresses_scroll = ttk.Scrollbar(self.failed_addresses_list)
        self.failed_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.failed_addresses_list.config(yscrollcommand=self.failed_addresses_scroll.set)
        self.failed_addresses_scroll.config(command=self.failed_addresses_list.yview)

        # Список удаленных адресов
        self.deleted_addresses_frame = ttk.LabelFrame(self.service_lists_paned, text="Удаленные адреса (0)")
        self.service_lists_paned.add(self.deleted_addresses_frame, weight=1)

        self.deleted_addresses_list = tk.Listbox(self.deleted_addresses_frame)
        self.deleted_addresses_list.pack(fill=tk.BOTH, expand=True)
        self.deleted_addresses_scroll = ttk.Scrollbar(self.deleted_addresses_list)
        self.deleted_addresses_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.deleted_addresses_list.config(yscrollcommand=self.deleted_addresses_scroll.set)
        self.deleted_addresses_scroll.config(command=self.deleted_addresses_list.yview)

        # Привязываем двойной клик для восстановления
        self.deleted_addresses_list.bind("<Double-Button-1>", self.restore_deleted_address)

        # Кнопка для очистки списка удаленных
        ttk.Button(
            self.deleted_addresses_frame,
            text="Очистить список",
            command=self.clear_deleted_list
        ).pack(fill=tk.X, pady=5)

        # Панель ручного ввода адреса и координат (правая часть)
        self.service_right_paned = ttk.PanedWindow(self.service_main_paned, orient=tk.VERTICAL)
        self.service_main_paned.add(self.service_right_paned, weight=1)

        # Панель ручного ввода адреса и координат
        self.manual_frame = ttk.LabelFrame(self.service_right_paned, text="Ручной ввод данных")
        self.service_right_paned.add(self.manual_frame, weight=3)

        # Фрейм для выбора типа карты (ниже фрейма ручного ввода)
        self.map_type_frame = ttk.LabelFrame(self.service_right_paned, text="Тип карты")
        self.service_right_paned.add(self.map_type_frame, weight=1)

        # Радиокнопки для выбора типа карты
        ttk.Radiobutton(
            self.map_type_frame,
            text="Стандартная",
            variable=self.map_type,
            value="normal",
            command=self.change_map_type
        ).pack(anchor=tk.W, padx=10, pady=5)

        ttk.Radiobutton(
            self.map_type_frame,
            text="Спутник",
            variable=self.map_type,
            value="satellite",
            command=self.change_map_type
        ).pack(anchor=tk.W, padx=10, pady=5)

        # Поле для адреса
        ttk.Label(self.manual_frame, text="Адрес:").pack(pady=(5, 0))
        self.address_entry = ttk.Entry(self.manual_frame)
        self.address_entry.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Поля для координат
        coord_frame = ttk.Frame(self.manual_frame)
        coord_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(coord_frame, text="Широта:").pack(side=tk.LEFT)
        self.lat_entry = ttk.Entry(coord_frame, width=12)
        self.lat_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(coord_frame, text="Долгота:").pack(side=tk.LEFT)
        self.lng_entry = ttk.Entry(coord_frame, width=12)
        self.lng_entry.pack(side=tk.LEFT, padx=(5, 0))

        # Кнопка добавления
        ttk.Button(
            self.manual_frame,
            text="Добавить вручную",
            command=self.add_manual
        ).pack(pady=5, fill=tk.X)

        # Кнопка добавления только адреса
        ttk.Button(
            self.manual_frame,
            text="Вставить только адрес",
            command=lambda: self.address_entry.insert(tk.END, self.root.clipboard_get())
        ).pack(pady=2, fill=tk.X)

        # Поле для ввода новой задачи с современным дизайном
        self.task_entry_frame = ttk.Frame(self.tasks_tab)
        self.task_entry_frame.pack(fill=tk.X, pady=(10, 5), padx=10)

        # Метка для поля ввода
        task_label = ttk.Label(self.task_entry_frame, text="Новая задача:", font=("Segoe UI", 10, "bold"))
        task_label.pack(anchor=tk.W, pady=(0, 5))

        # Контейнер для поля ввода и кнопки
        input_container = ttk.Frame(self.task_entry_frame)
        input_container.pack(fill=tk.X)

        self.task_entry = ttk.Entry(
            input_container,
            font=("Segoe UI", 10),
            style="info.TEntry"  # Используем стиль info для выделения
        )
        self.task_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.task_entry.bind("<Return>", self.add_task)

        # Современная кнопка добавления
        add_btn = ttk.Button(
            input_container,
            text="➕ Добавить",
            style="success.TButton",  # Зеленая кнопка
            command=self.add_task
        )
        add_btn.pack(side=tk.RIGHT)

        # Список задач с современным дизайном
        self.tasks_list_frame = ttk.Frame(self.tasks_tab)
        self.tasks_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))

        # Создаем контейнер для списка с рамкой
        list_container = ttk.Frame(self.tasks_list_frame, style="Card.TFrame")
        list_container.pack(fill=tk.BOTH, expand=True)

        # Настраиваем кастомный стиль для списка задач
        self.style.configure("Tasks.Treeview",
                       background="#2b3e50",  # Темно-синий фон
                       foreground="white",    # Белый текст
                       fieldbackground="#2b3e50",  # Фон полей
                       font=("Segoe UI", 10),  # Увеличенный шрифт
                       rowheight=25)  # Увеличенная высота строки (по умолчанию ~20)

        self.style.configure("Tasks.Treeview.Heading",
                       background="#34495e",  # Менее яркий серо-синий для заголовков
                       foreground="#ecf0f1",  # Светло-серый текст заголовков
                       font=("Segoe UI", 9, "bold"))  # Жирный шрифт для заголовков

        # Создаем Treeview для отображения задач в виде таблицы
        self.tasks_list = ttk.Treeview(
            list_container,
            columns=("status", "date", "task", "driver"),
            show="headings",
            style="Tasks.Treeview"  # Используем кастомный стиль
        )

        # Настраиваем заголовки столбцов с иконками
        self.tasks_list.heading("status", text="📌")
        self.tasks_list.heading("date", text="📅 Дата")
        self.tasks_list.heading("task", text="📝 Задача")
        self.tasks_list.heading("driver", text="👤 Водитель")

        # Настраиваем ширину столбцов
        self.tasks_list.column("status", width=40, minwidth=40, stretch=False)  # Статус
        self.tasks_list.column("date", width=90, minwidth=90, stretch=False)  # Дата
        self.tasks_list.column("task", width=200, stretch=True)  # Задача (растягивается)
        self.tasks_list.column("driver", width=120, minwidth=120, stretch=False)  # Водитель

        # Настраиваем стиль скроллбара
        self.style.configure("Tasks.Vertical.TScrollbar",
                       background="#34495e",  # Фон скроллбара
                       troughcolor="#2b3e50",  # Цвет желобка
                       bordercolor="#34495e",  # Цвет границы
                       arrowcolor="#ecf0f1",   # Цвет стрелок
                       darkcolor="#2c3e50",    # Темный цвет
                       lightcolor="#34495e")   # Светлый цвет

        # Добавляем прокрутку
        self.tasks_scroll = ttk.Scrollbar(
            list_container,
            orient="vertical",
            command=self.tasks_list.yview,
            style="Tasks.Vertical.TScrollbar"
        )
        self.tasks_list.configure(yscrollcommand=self.tasks_scroll.set)

        # Размещаем список и прокрутку
        self.tasks_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.tasks_scroll.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 5), pady=5)

        # Привязываем двойной клик для редактирования задачи
        self.tasks_list.bind("<Double-1>", self.edit_task)

        # Привязываем контекстное меню
        self.tasks_list.bind("<Button-3>", self.show_tasks_context_menu)

        # Панель с кнопками действий
        actions_frame = ttk.Frame(self.tasks_list_frame)
        actions_frame.pack(fill=tk.X, pady=(10, 0))

        # Кнопки действий с современным дизайном
        edit_btn = ttk.Button(
            actions_frame,
            text="✏️ Редактировать",
            style="info.TButton",
            command=self.edit_task
        )
        edit_btn.pack(side=tk.LEFT, padx=(0, 5))

        delete_btn = ttk.Button(
            actions_frame,
            text="🗑️ Удалить",
            style="danger.TButton",
            command=self.delete_task
        )
        delete_btn.pack(side=tk.LEFT, padx=5)

        # Статистика задач
        self.stats_label = ttk.Label(
            actions_frame,
            text="",
            font=("Segoe UI", 9),
            foreground="#6c757d"
        )
        self.stats_label.pack(side=tk.RIGHT)

        # Кнопки загрузки файлов перемещены в фильтр для списка загружаемых адресов

        # Кнопка "Сохранить избранное" перемещена в фрейм с выбором водителя



        # Выбор типа карты перемещен во вкладку "Служебные"

    def add_to_selected(self, event=None):
            selection = self.all_addresses_list.selection()
            if not selection:
                return

            # Получаем выбранный элемент
            item = self.all_addresses_list.item(selection[0])
            # Получаем адрес из первого столбца
            address = item['values'][0]

            if self.list_selector.get() == "list1":
                target_markers = self.selected_markers
                target_list = self.selected_addresses_list
                marker_color = "blue"
            else:
                target_markers = self.selected_markers2
                target_list = self.selected_addresses_list2
                marker_color = "green"

            if address in target_markers: # Уже в целевом списке
                return

            # Удаляем из основного списка отображения
            self.all_addresses_list.delete(selection[0])

            # --- Начало возможного исправления ---
            # Удаляем старый маркер (если есть) с карты
            if address in self.markers:
                old_marker_on_map = self.markers.pop(address) # Удаляем из self.markers и получаем объект
                if old_marker_on_map is not None:
                    # Убедимся, что он все еще отслеживается картой перед удалением
                    if old_marker_on_map in self.map_widget.canvas_marker_list:
                        old_marker_on_map.delete()
                    else:
                        # Если его нет в списке карты, но он есть у нас,
                        # это странно, но все равно попытаемся вызвать delete(),
                        # т.к. он может быть "потерян" для списка, но все еще на холсте.
                        # Метод delete() самого маркера должен быть идемпотентным (безопасным при повторном вызове).
                        old_marker_on_map.delete()

                    # Можно добавить принудительное обновление холста здесь для теста
                    # self.map_widget.canvas.update() # или self.root.update_idletasks()
                    # time.sleep(0.1) # Небольшая пауза для визуальной проверки, если будете отлаживать пошагово

            # Также удаляем из original_markers, если он там был как объект маркера,
            # который мы только что удалили с карты.
            # Это важно, если original_markers используется для восстановления маркеров.
            # Но в вашем коде original_markers[address] может ссылаться на тот же объект,
            # что и self.markers[address] до его удаления.
            # Если original_markers должен хранить только "исходные данные" (координаты),
            # а не активные объекты маркеров, то его трогать не надо при простом удалении с карты.
            # Однако, если при сохранении вы полностью удаляете адрес отовсюду,
            # то и из original_markers его тоже нужно будет убрать (что вы и делаете в save_selected_with_driver).

            # --- Конец возможного исправления ---

            if address in self.original_markers:
                # Если original_markers[address] это объект маркера, а не просто данные,
                # и этот объект был удален выше, то .position может вызвать ошибку.
                # Безопаснее получить позицию до удаления или хранить позицию отдельно.
                # Предположим, что original_markers[address] все еще валиден для получения позиции,
                # или что старый маркер (old_marker_on_map) все еще имеет доступные атрибуты .position
                # даже после .delete() (обычно так и есть).
                # Если old_marker_on_map определен выше, используем его позицию.
                try:
                    # Попытка получить позицию из удаленного маркера, если он был
                    lat, lng = old_marker_on_map.position if 'old_marker_on_map' in locals() and old_marker_on_map else self.original_markers[address].position
                except AttributeError: # Если original_markers[address] уже не маркер или у old_marker_on_map нет position
                    lat, lng = self.geocode_address(address) # Геокодируем заново как запасной вариант
                    if lat is None or lng is None:
                        self.failed_addresses.append(address)
                        self.failed_addresses_list.insert(tk.END, address)
                        # Вернуть удаленный из self.markers адрес обратно, если он там был,
                        # или обработать эту ситуацию по-другому.
                        # self.markers[address] = old_marker_on_map # Пример
                        return

            else:
                lat, lng = self.geocode_address(address)
                if lat is None or lng is None:
                    self.failed_addresses.append(address)
                    self.failed_addresses_list.insert(tk.END, address)
                    return

            new_marker = self.map_widget.set_marker(
                lat, lng,
                text=address,
                marker_color_circle=marker_color,
                marker_color_outside=marker_color,
                text_color=marker_color
            )

            target_markers[address] = new_marker
            self.markers[address] = new_marker # Теперь self.markers указывает на новый цветной маркер
            target_list.insert(tk.END, address)

            # Обновляем счетчики
            self.update_counts()

            # Обновляем список доступных адресов для распределения, если оно активно
            if hasattr(self, 'distribution_params'):
                print(f"🔄 Обновляем параметры распределения после добавления адреса '{address}' в список")
                self.update_available_addresses_for_distribution()
            else:
                print("ℹ️ Распределение не активно, обновление не требуется")

    def return_to_all(self, event=None):
        """Возвращение адреса из избранного в основной список с полным пересозданием маркера"""
        selection = self.selected_addresses_list.curselection()
        if not selection:
            return

        address = self.selected_addresses_list.get(selection[0])

        if address not in self.selected_markers:
            return

        # Получаем маркер перед удалением
        marker = self.selected_markers[address]
        lat, lng = marker.position

        # Удаляем из избранного
        self.selected_addresses_list.delete(selection[0])

        # Удаляем маркер с карты
        if marker in self.map_widget.canvas_marker_list:
            marker.delete()
            try:
                self.map_widget.canvas_marker_list.remove(marker)
            except ValueError:
                pass

        # Удаляем из словарей
        if address in self.selected_markers:
            del self.selected_markers[address]
        if address in self.markers:
            del self.markers[address]

        # Обновляем карту
        self.map_widget.update()

        # Создаем новый обычный маркер (коричневый)
        new_marker = self.map_widget.set_marker(
            lat, lng,
            text=address,
            marker_color_circle="brown",
            marker_color_outside="brown"
        )

        # Обновляем хранилища
        self.markers[address] = new_marker
        self.original_markers[address] = new_marker

        # Добавляем в основной список с суммой и весом
        current_filter = self.filter_var.get().lower()
        if not current_filter or current_filter in address.lower():
            # Получаем значения суммы и веса для адреса
            address_sum = self.address_values.get(address, {}).get('sum', 0.0)
            address_weight = self.address_values.get(address, {}).get('weight', 0.0)

            # Добавляем адрес в список с суммой и весом
            self.all_addresses_list.insert('', 'end', values=(
                address,
                f"{address_sum:.2f}",
                f"{address_weight:.3f}"
            ))

        # Обновляем счетчики
        self.update_counts()

        # Выводим отладочное сообщение
        print(f"🔄 Адрес '{address}' возвращен из списка 1 в основной список")

        # Обновляем список доступных адресов для распределения, если оно активно
        if hasattr(self, 'distribution_params'):
            print(f"🔄 Обновляем параметры распределения после возврата адреса '{address}'")
            self.update_available_addresses_for_distribution()
        else:
            print("ℹ️ Распределение не активно, обновление не требуется")

    def return_to_all_from_list2(self, event=None):
        """Возврат адреса из второго списка в основной с полным пересозданием маркера"""
        selection = self.selected_addresses_list2.curselection()
        if not selection:
            return

        address = self.selected_addresses_list2.get(selection[0])

        if address not in self.selected_markers2:
            return

        # Получаем маркер перед удалением
        marker = self.selected_markers2[address]
        lat, lng = marker.position

        # Удаляем из всех хранилищ
        self.selected_addresses_list2.delete(selection[0])
        del self.selected_markers2[address]

        # Сохраняем ссылку на маркер для последующей фильтрации
        marker_to_remove = marker

        # Полностью удаляем маркер с карты
        if marker in self.map_widget.canvas_marker_list:
            marker.delete()
        if address in self.selected_markers2:
            marker = self.selected_markers2.pop(address)
            marker.delete()

        # Удаляем из основного словаря маркеров
        if address in self.markers:
            del self.markers[address]

        if marker and marker in self.map_widget.canvas_marker_list:
            marker.delete()

        # Фильтруем canvas_marker_list
        self.map_widget.canvas_marker_list = [
            m for m in self.map_widget.canvas_marker_list
            if m != marker_to_remove
        ]

        # Обновляем карту
        self.map_widget.update()

        # Создаем новый обычный маркер
        new_marker = self.map_widget.set_marker(
            lat, lng,
            text=address
        )

        # Обновляем хранилища
        self.original_markers[address] = new_marker
        self.markers[address] = new_marker

        # Добавляем в основной список с суммой и весом, если соответствует фильтру
        current_filter = self.filter_var.get().lower()
        if not current_filter or current_filter in address.lower():
            # Получаем значения суммы и веса для адреса
            address_sum = self.address_values.get(address, {}).get('sum', 0.0)
            address_weight = self.address_values.get(address, {}).get('weight', 0.0)

            # Добавляем адрес в список с суммой и весом
            self.all_addresses_list.insert('', 'end', values=(
                address,
                f"{address_sum:.2f}",
                f"{address_weight:.3f}"
            ))

        self.update_counts()

    def update_counts(self):
        """Обновление всех счетчиков"""
        self.update_address_count()

        # Подсчитываем сумму и вес для списка 1
        self.list1_sum = sum(self.address_values.get(address, {}).get('sum', 0.0) for address in self.selected_markers)
        self.list1_weight = sum(self.address_values.get(address, {}).get('weight', 0.0) for address in self.selected_markers)

        # Подсчитываем сумму и вес для списка 2
        self.list2_sum = sum(self.address_values.get(address, {}).get('sum', 0.0) for address in self.selected_markers2)
        self.list2_weight = sum(self.address_values.get(address, {}).get('weight', 0.0) for address in self.selected_markers2)

        # Обновляем заголовки списков с информацией о сумме и весе
        self.selected_addresses_frame.config(
            text=f"Список 1 ({len(self.selected_markers)}) - Сумма: {self.list1_sum:.2f}, Вес: {self.list1_weight:.3f}"
        )
        self.selected_addresses_frame2.config(
            text=f"Список 2 ({len(self.selected_markers2)}) - Сумма: {self.list2_sum:.2f}, Вес: {self.list2_weight:.3f}"
        )


    def init_geocoders(self):
        """Инициализация всех геокодеров"""
        # Google Maps
        try:
            self.gmaps_client = googlemaps.Client(key=self.GOOGLE_MAPS_API_KEY)
            test_result = self.gmaps_client.geocode('Red Square, Moscow', region='ru')
            if not test_result:
                print("Google API работает, но не возвращает результаты")
        except Exception as e:
            print(f"Ошибка Google API: {e}")
            self.gmaps_client = None

        # Яндекс Геокодер
        self.yandex_geocoder = YandexGeocoder(self.YANDEX_MAPS_API_KEY)


    def show_deleted_context_menu(self, event):
        """Показывает контекстное меню для списка удаленных"""
        try:
            self.deleted_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.deleted_menu.grab_release()

    def copy_from_list(self, list_widget):
        """Копирует выделенный адрес из указанного списка"""
        selection = list_widget.curselection()
        if selection:
            address = list_widget.get(selection[0])
            self.root.clipboard_clear()
            self.root.clipboard_append(address)

    def add_to_deleted(self, address):
        """Добавляет адрес в список удаленных"""
        if address and address not in self.deleted_addresses:
            self.deleted_addresses.append(address)
            self.deleted_addresses_list.insert(tk.END, address)

            # Прокручиваем список к добавленному адресу
            self.deleted_addresses_list.see(tk.END)

            # Обновляем интерфейс
            self.deleted_addresses_frame.config(text=f"Удаленные адреса ({len(self.deleted_addresses)})")
            self.deleted_addresses_list.update()

            # Принудительное обновление интерфейса
            self.root.update_idletasks()

    def restore_deleted_address(self, event=None):
        """Восстанавливает адрес из удаленных и добавляет его на карту"""
        selection = self.deleted_addresses_list.curselection()
        if not selection:
            return

        address = self.deleted_addresses_list.get(selection[0])

        # Удаляем из списка удаленных
        self.deleted_addresses.remove(address)  # Удаляем из внутреннего списка
        self.deleted_addresses_list.delete(selection[0])  # Удаляем из виджета

        # Обновляем заголовок
        self.deleted_addresses_frame.config(text=f"Удаленные адреса ({len(self.deleted_addresses)})")

        # Принудительное обновление интерфейса
        self.root.update_idletasks()

        # Получаем координаты для адреса
        lat, lng = self.geocode_address(address)

        if lat is not None and lng is not None:
            # Добавляем маркер на карту
            marker = self.map_widget.set_marker(lat, lng, text=address)
            self.markers[address] = marker
            self.original_markers[address] = marker

            # Добавляем в основной список с суммой и весом
            # Получаем значения суммы и веса для адреса (или устанавливаем нулевые значения)
            address_sum = self.address_values.get(address, {}).get('sum', 0.0)
            address_weight = self.address_values.get(address, {}).get('weight', 0.0)

            # Если адрес не имеет значений суммы и веса, добавляем их
            if address not in self.address_values:
                self.address_values[address] = {'sum': 0.0, 'weight': 0.0}

            # Добавляем адрес в список с суммой и весом
            self.all_addresses_list.insert('', 'end', values=(
                address,
                f"{address_sum:.2f}",
                f"{address_weight:.3f}"
            ))

            # Обновляем счетчики
            self.update_counts()

            # Центрируем карту на восстановленном адресе
            self.map_widget.set_position(lat, lng)

            messagebox.showinfo("Восстановление", f"Адрес '{address}' успешно восстановлен и добавлен на карту")
        else:
            # Если не удалось получить координаты, вставляем в поле ручного ввода
            self.address_entry.delete(0, tk.END)
            self.address_entry.insert(0, address)
            messagebox.showwarning("Предупреждение",
                                 f"Не удалось получить координаты для адреса '{address}'.\n"
                                 "Адрес вставлен в поле ручного ввода.")

    def clear_deleted_list(self):
        """Очищает список удаленных"""
        if not self.deleted_addresses:
            return

        if messagebox.askyesno("Подтверждение", "Очистить весь список удаленных адресов?"):
            self.deleted_addresses = []  # Очищаем внутренний список
            self.deleted_addresses_list.delete(0, tk.END)  # Очищаем виджет

            # Обновляем заголовок
            self.deleted_addresses_frame.config(text="Удаленные адреса (0)")

            # Принудительное обновление интерфейса
            self.root.update_idletasks()

    def update_selected_count(self):
        """Обновляет отображение количества избранных адресов"""
        # Подсчитываем сумму и вес для списка 1
        self.list1_sum = sum(self.address_values.get(address, {}).get('sum', 0.0) for address in self.selected_markers)
        self.list1_weight = sum(self.address_values.get(address, {}).get('weight', 0.0) for address in self.selected_markers)

        # Подсчитываем сумму и вес для списка 2
        self.list2_sum = sum(self.address_values.get(address, {}).get('sum', 0.0) for address in self.selected_markers2)
        self.list2_weight = sum(self.address_values.get(address, {}).get('weight', 0.0) for address in self.selected_markers2)

        # Обновляем заголовки списков с информацией о сумме и весе
        self.selected_addresses_frame.config(
            text=f"Список 1 ({len(self.selected_markers)}) - Сумма: {self.list1_sum:.2f}, Вес: {self.list1_weight:.3f}"
        )
        self.selected_addresses_frame2.config(
            text=f"Список 2 ({len(self.selected_markers2)}) - Сумма: {self.list2_sum:.2f}, Вес: {self.list2_weight:.3f}"
        )

    def copy_selected(self, event=None):
        """Копирование выделенного текста в разных форматах"""
        widget = self.root.focus_get()

        if widget in [self.all_addresses_list, self.selected_addresses_list, self.failed_addresses_list]:
            selection = widget.curselection()
            if selection:
                address = widget.get(selection[0])
                if address in self.markers:
                    marker = self.markers[address]
                    # Формат 1: координаты, адрес (как в примере)
                    self.clipboard = f"{marker.position[0]}, {marker.position[1]}\n{address}"
                    # Формат 2: для вставки в поля (адрес[tab]широта[tab]долгота)
                    self.clipboard_tsv = f"{address}\t{marker.position[0]}\t{marker.position[1]}"
                else:
                    self.clipboard = address
                    self.clipboard_tsv = address
                self.root.clipboard_clear()
                self.root.clipboard_append(self.clipboard)

    def paste_to_entry(self, event=None):
        """Вставка данных из буфера с улучшенным определением формата"""
        try:
            clipboard_text = self.root.clipboard_get().strip()

            # Очищаем все поля перед вставкой
            self.address_entry.delete(0, tk.END)
            self.lat_entry.delete(0, tk.END)
            self.lng_entry.delete(0, tk.END)

            # Разбиваем текст на строки
            lines = clipboard_text.split('\n')

            # Случай 1: Две строки - координаты и адрес (как при копировании из списка)
            if len(lines) == 2:
                first_line = lines[0].strip()
                second_line = lines[1].strip()

                # Проверяем, является ли первая строка координатами
                if ',' in first_line and all(self.is_valid_coordinate(x.strip()) for x in first_line.split(',')[:2]):
                    coords = first_line.split(',')
                    self.lat_entry.insert(0, coords[0].strip())
                    self.lng_entry.insert(0, coords[1].strip())
                    self.address_entry.insert(0, second_line)
                else:
                    # Если первая строка не координаты, считаем весь текст адресом
                    self.address_entry.insert(0, clipboard_text.replace('\n', ' '))

            # Случай 2: Одна строка с табуляцией (адрес\tширота\tдолгота)
            elif '\t' in clipboard_text:
                parts = clipboard_text.split('\t')
                if len(parts) >= 3:
                    self.address_entry.insert(0, parts[0].strip())
                    self.lat_entry.insert(0, parts[1].strip())
                    self.lng_entry.insert(0, parts[2].strip())
                else:
                    self.address_entry.insert(0, clipboard_text.replace('\t', ' '))

            # Случай 3: Одна строка с запятой (возможно координаты)
            elif ',' in clipboard_text:
                parts = [x.strip() for x in clipboard_text.split(',')]
                # Проверяем, что это координаты (два числа)
                if len(parts) >= 2 and all(self.is_valid_coordinate(x) for x in parts[:2]):
                    self.lat_entry.insert(0, parts[0])
                    self.lng_entry.insert(0, parts[1])
                else:
                    # Если не координаты, то это адрес с запятыми
                    self.address_entry.insert(0, clipboard_text)

            # Случай 4: Просто текст (адрес)
            else:
                self.address_entry.insert(0, clipboard_text)

        except tk.TclError:
            pass  # Буфер обмена пуст

    def is_valid_coordinate(self, value):
        """Проверяет, является ли значение корректной координатой"""
        try:
            num = float(value)
            return True
        except ValueError:
            return False

    def show_context_menu(self, event):
        """Показ контекстного меню"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_all_addresses_context_menu(self, event):
        """Показ контекстного меню для общего списка адресов"""
        try:
            # Получаем элемент, на котором был клик
            item = self.all_addresses_list.identify_row(event.y)
            if item:
                # Выделяем элемент
                self.all_addresses_list.selection_set(item)
                # Показываем меню
                self.all_addresses_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.all_addresses_menu.grab_release()

    # Метод show_drivers_context_menu удален, так как его функциональность перенесена в список с расписанием

    def show_tasks_context_menu(self, event):
        """Показывает контекстное меню для списка задач"""
        try:
            # Получаем элемент, на котором был клик
            item = self.tasks_list.identify_row(event.y)
            if item:
                # Выделяем элемент
                self.tasks_list.selection_set(item)
                # Показываем меню
                self.tasks_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.tasks_menu.grab_release()

    def delete_task(self):
        """Удаляет выбранную задачу из списка и удаляет её маркер с карты"""
        selection = self.tasks_list.selection()
        if not selection:
            return

        task_id = int(selection[0])
        if task_id not in self.tasks:
            return

        # Получаем информацию о задаче перед удалением
        task_info = self.tasks[task_id]
        address = task_info.get('address', '')

        # Если у задачи есть адрес, удаляем его маркер с карты
        if address:
            print(f"Удаление маркера для адреса '{address}' при удалении задачи")
            self.remove_task_address_from_map(address)

        # Удаляем задачу из словаря
        del self.tasks[task_id]

        # Обновляем заголовок фрейма
        self.tasks_frame.config(text=f"📋 Управление задачами ({len(self.tasks)})")

        # Обновляем отображение с сортировкой
        self.update_tasks_display()

    def add_driver_to_registry(self, event=None):
        """Добавляет нового водителя в реестр"""
        driver_name = self.driver_entry.get().strip()
        if not driver_name:
            return

        # Проверяем, есть ли уже такой водитель в реестре
        if driver_name in self.drivers_registry:
            messagebox.showwarning("Предупреждение", f"Водитель '{driver_name}' уже есть в реестре")
            return

        # Добавляем водителя в реестр
        self.drivers_registry[driver_name] = {'region': ''}

        # Добавляем водителя в список
        self.drivers_registry_list.insert('', 'end', values=(driver_name, ''))

        # Очищаем поле ввода
        self.driver_entry.delete(0, tk.END)

        # Обновляем заголовок фрейма
        self.drivers_list_frame.config(text=f"Водители ({len(self.drivers_registry)})")

        # Обновляем значения в комбобоксе водителя
        self.driver_combobox.config(values=list(self.drivers_registry.keys()))

    def edit_driver(self, event=None):
        """Открывает окно редактирования водителя"""
        # Получаем выбранного водителя
        selection = self.drivers_registry_list.selection()
        if not selection:
            return

        # Получаем данные выбранного водителя
        item = self.drivers_registry_list.item(selection[0])
        driver_name, region = item['values']

        if driver_name not in self.drivers_registry:
            return

        # Создаем окно редактирования
        edit_window = ttk.Toplevel(self.root)
        edit_window.title("Редактирование водителя")
        edit_window.geometry("400x200")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Центрируем окно на экране
        edit_window.update_idletasks()
        self.center_window(edit_window)

        # Фрейм для полей редактирования
        edit_frame = ttk.Frame(edit_window, padding=10)
        edit_frame.pack(fill=tk.BOTH, expand=True)

        # Поле для имени водителя
        ttk.Label(edit_frame, text="Водитель:").grid(row=0, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar(value=driver_name)
        driver_entry = ttk.Entry(edit_frame, textvariable=driver_var)
        driver_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для региона
        ttk.Label(edit_frame, text="Регион:").grid(row=1, column=0, sticky=tk.W, pady=5)
        region_var = tk.StringVar(value=region)
        region_entry = ttk.Entry(edit_frame, textvariable=region_var)
        region_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Настраиваем веса строк и столбцов
        edit_frame.columnconfigure(1, weight=1)

        # Добавляем горячие клавиши
        edit_window.bind('<Control-v>', lambda e: self.paste_to_widget(e.widget))
        edit_window.bind('<Control-c>', lambda e: self.copy_from_widget(e.widget))

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.save_driver_edit(
                edit_window, driver_name, driver_var.get(), region_var.get()
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=edit_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def save_driver_edit(self, window, old_driver_name, new_driver_name, region):
        """Сохраняет изменения в данных водителя"""
        if not new_driver_name.strip():
            messagebox.showwarning("Предупреждение", "Имя водителя не может быть пустым")
            return

        # Проверяем, изменилось ли имя водителя
        if old_driver_name != new_driver_name and new_driver_name in self.drivers_registry:
            messagebox.showwarning("Предупреждение", f"Водитель '{new_driver_name}' уже есть в реестре")
            return

        # Обновляем данные в реестре
        if old_driver_name != new_driver_name:
            # Если имя изменилось, создаем новую запись и удаляем старую
            self.drivers_registry[new_driver_name] = {'region': region}
            del self.drivers_registry[old_driver_name]

            # Обновляем имя водителя в задачах
            for task_id, task_info in self.tasks.items():
                if task_info['driver'] == old_driver_name:
                    task_info['driver'] = new_driver_name
                    self.tasks_list.item(str(task_id), values=(task_info['date'], task_info['task'], new_driver_name))
        else:
            # Если имя не изменилось, просто обновляем регион
            self.drivers_registry[old_driver_name]['region'] = region

        # Обновляем отображение в списке
        for item_id in self.drivers_registry_list.get_children():
            item = self.drivers_registry_list.item(item_id)
            if item['values'][0] == old_driver_name:
                self.drivers_registry_list.item(item_id, values=(new_driver_name, region))
                break

        # Обновляем значения в комбобоксе водителя
        self.driver_combobox.config(values=list(self.drivers_registry.keys()))

        # Применяем фильтр
        self.apply_driver_filter()

        # Закрываем окно редактирования
        window.destroy()

    def apply_driver_filter(self):
        """Применяет фильтр к списку водителей"""
        filter_text = self.driver_filter_var.get().lower()

        # Очищаем список
        for item in self.drivers_registry_list.get_children():
            self.drivers_registry_list.delete(item)

        # Заполняем список с учетом фильтра
        for driver_name, info in self.drivers_registry.items():
            region = info.get('region', '')
            if not filter_text or filter_text in region.lower():
                self.drivers_registry_list.insert('', 'end', values=(driver_name, region))

        # Обновляем заголовок фрейма
        visible_count = len(self.drivers_registry_list.get_children())
        total_count = len(self.drivers_registry)
        self.drivers_list_frame.config(text=f"Водители ({visible_count}/{total_count})")

    def reset_driver_filter(self):
        """Сбрасывает фильтр списка водителей"""
        if self.driver_filter_var.get():
            self.driver_filter_var.set("")
            self.apply_driver_filter()

    def delete_driver_from_registry(self):
        """Удаляет выбранного водителя из реестра"""
        selection = self.drivers_registry_list.selection()
        if not selection:
            self.show_modern_message("Выберите водителя", "Выберите водителя для удаления", "warning")
            return

        # Получаем данные выбранного водителя
        item = self.drivers_registry_list.item(selection[0])
        driver_name = item['values'][0]

        # Проверяем, используется ли водитель в задачах
        used_in_tasks = False
        for task_id, task_info in self.tasks.items():
            if task_info['driver'] == driver_name:
                used_in_tasks = True
                break

        if used_in_tasks:
            if not messagebox.askyesno("Подтверждение",
                                     f"Водитель '{driver_name}' используется в задачах. Удалить его из реестра?"):
                return

        # Удаляем водителя из реестра
        del self.drivers_registry[driver_name]

        # Удаляем водителя из списка
        self.drivers_registry_list.delete(selection[0])

        # Обновляем заголовок фрейма
        visible_count = len(self.drivers_registry_list.get_children())
        total_count = len(self.drivers_registry)
        self.drivers_list_frame.config(text=f"Водители ({visible_count}/{total_count})")

        # Обновляем значения в комбобоксе водителя
        self.driver_combobox.config(values=list(self.drivers_registry.keys()))

    def show_drivers_registry_menu(self, event):
        """Показывает контекстное меню для реестра водителей"""
        try:
            # Получаем элемент, на котором был клик
            item = self.drivers_registry_list.identify_row(event.y)
            if item:
                # Выделяем элемент
                self.drivers_registry_list.selection_set(item)
                # Показываем меню
                self.drivers_registry_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.drivers_registry_menu.grab_release()

    def copy_from_registry(self):
        """Копирует имя выбранного водителя из реестра в буфер обмена"""
        selection = self.drivers_registry_list.selection()
        if selection:
            item = self.drivers_registry_list.item(selection[0])
            driver_name = item['values'][0]
            self.root.clipboard_clear()
            self.root.clipboard_append(driver_name)

    def save_drivers_registry(self):
        """Сохранение реестра водителей в Excel файл"""
        registry_file = "drivers_registry.xlsx"

        try:
            # Создаем DataFrame из реестра водителей
            data = []
            for driver_name, info in self.drivers_registry.items():
                data.append({
                    'Водитель': driver_name,
                    'Регион': info.get('region', '')
                })

            if not data:
                print("Реестр водителей пуст, нечего сохранять")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(registry_file, index=False)
            print(f"Сохранено {len(data)} водителей в реестр")
        except Exception as e:
            print(f"Ошибка при сохранении реестра водителей: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить реестр водителей:\n{str(e)}")

    def load_drivers_registry(self):
        """Загрузка реестра водителей из Excel файла"""
        registry_file = "drivers_registry.xlsx"

        if not os.path.exists(registry_file):
            print("Файл реестра водителей не найден, будет создан новый")
            return

        try:
            df = pd.read_excel(registry_file)

            # Проверяем наличие необходимых столбцов
            if 'Водитель' in df.columns:
                # Очищаем текущий реестр
                self.drivers_registry.clear()

                # Очищаем список
                for item in self.drivers_registry_list.get_children():
                    self.drivers_registry_list.delete(item)

                for _, row in df.iterrows():
                    driver_name = row['Водитель']
                    region = row['Регион'] if 'Регион' in df.columns and pd.notna(row['Регион']) else ''

                    if pd.notna(driver_name) and driver_name.strip():
                        # Добавляем водителя в реестр
                        self.drivers_registry[driver_name] = {'region': region}

                        # Добавляем водителя в список
                        self.drivers_registry_list.insert('', 'end', values=(driver_name, region))

                # Обновляем заголовок фрейма
                self.drivers_list_frame.config(text=f"Водители ({len(self.drivers_registry)})")

                # Обновляем значения в комбобоксе водителя
                self.driver_combobox.config(values=list(self.drivers_registry.keys()))

                print(f"Загружено {len(self.drivers_registry)} водителей из реестра")
            else:
                print("Некорректный формат файла реестра водителей")
        except Exception as e:
            print(f"Ошибка при загрузке реестра водителей: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить реестр водителей:\n{str(e)}")

    # Метод copy_driver_name удален, так как его функциональность перенесена в список с расписанием

    def load_additional_excel(self):
        """Загрузка дополнительных адресов из Excel файла"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # Читаем Excel файл
            df = pd.read_excel(file_path)

            if len(df.columns) < 1:
                messagebox.showerror("Ошибка", "Файл должен содержать хотя бы один столбец с адресами")
                return

            # Определяем столбец с адресами
            address_col = df.columns[0]

            progress_window = ttk.Toplevel(self.root)
            progress_window.title("Обработка адресов")
            progress_label = ttk.Label(progress_window, text="Добавление адресов...")
            progress_label.pack(pady=10)
            progress = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress.pack(pady=10)
            progress_window.update()

            total = len(df)
            processed = 0
            success_count = 0
            new_failed = 0

            for _, row in df.iterrows():
                address = str(row[address_col]) if pd.notna(row[address_col]) else ""

                if address and address.strip() and address not in self.original_markers:
                    lat, lng = None, None

                    # Пробуем получить значения веса и суммы из вашего файла
                    address_sum = 0.0
                    address_weight = 0.0

                    # В вашем файле:
                    # Столбец 'Адрес разгрузки' - адрес
                    # Столбец 'Вес брутто (кг)' - вес
                    # Столбец 'Сумма с НДС' - сумма
                    try:
                        if 'Вес брутто (кг)' in df.columns and pd.notna(row['Вес брутто (кг)']):
                            address_weight = float(row['Вес брутто (кг)'])
                            print(f"Получен вес из столбца 'Вес брутто (кг)': {address_weight:.3f}")
                        elif len(df.columns) > 1 and pd.notna(row[df.columns[1]]):
                            address_weight = float(row[df.columns[1]])
                            print(f"Получен вес из второго столбца: {address_weight:.3f}")

                        if 'Сумма с НДС' in df.columns and pd.notna(row['Сумма с НДС']):
                            address_sum = float(row['Сумма с НДС'])
                            print(f"Получена сумма из столбца 'Сумма с НДС': {address_sum:.2f}")
                        elif len(df.columns) > 2 and pd.notna(row[df.columns[2]]):
                            address_sum = float(row[df.columns[2]])
                            print(f"Получена сумма из третьего столбца: {address_sum:.2f}")

                        print(f"Обработан дополнительный адрес: {address}, вес: {address_weight:.3f}, сумма: {address_sum:.2f}")
                    except (ValueError, TypeError) as e:
                        print(f"Ошибка при обработке значений для адреса {address}: {e}")
                        address_weight = 0.0
                        address_sum = 0.0

                    # Координаты будем получать через геокодирование
                    lat, lng = None, None

                    # Если координат нет в файле или они некорректны, геокодируем
                    if lat is None or lng is None or not self.validate_coordinates(lat, lng):
                        lat, lng = self.geocode_address(address)

                    if lat is not None and lng is not None:
                        # Используем коричневый цвет для маркеров (как было раньше)
                        marker = self.map_widget.set_marker(
                            lat, lng,
                            text=address,
                            marker_color_circle="brown",
                            marker_color_outside="brown"
                        )
                        self.markers[address] = marker
                        self.original_markers[address] = marker

                        # Сохраняем значения суммы и веса для адреса
                        self.address_values[address] = {
                            'sum': address_sum,
                            'weight': address_weight
                        }

                        if address not in self.selected_markers and address not in self.selected_markers2:
                            # Добавляем адрес в список с суммой и весом
                            self.all_addresses_list.insert('', 'end', values=(
                                address,
                                f"{address_sum:.2f}",
                                f"{address_weight:.3f}"
                            ))
                        success_count += 1
                    else:
                        self.failed_addresses.append(address)
                        self.failed_addresses_list.insert(tk.END, address)
                        new_failed += 1

                processed += 1
                progress['value'] = (processed / total) * 100
                progress_label.config(text=f"Обработано: {processed}/{total} | Новые: {success_count} | Ошибки: {new_failed}")
                progress_window.update()

            progress_window.destroy()

            if success_count > 0:
                messagebox.showinfo("Результат",
                                  f"Добавлено новых адресов: {success_count}\n"
                                  f"Не удалось обработать: {new_failed}")
            else:
                messagebox.showwarning("Предупреждение", "Не добавлено ни одного нового адреса")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить файл:\n{str(e)}")

        self.update_address_count()

    def update_address_count(self):
        """Обновляет отображение количества адресов"""
        total_count = len([a for a in self.original_markers if a not in self.selected_markers and a not in self.selected_markers2])
        visible_count = len(self.all_addresses_list.get_children())

        # Подсчитываем общую сумму и вес для всех адресов в основном списке
        total_sum = sum(self.address_values.get(a, {}).get('sum', 0.0)
                      for a in self.original_markers
                      if a not in self.selected_markers and a not in self.selected_markers2)
        total_weight = sum(self.address_values.get(a, {}).get('weight', 0.0)
                         for a in self.original_markers
                         if a not in self.selected_markers and a not in self.selected_markers2)

        self.all_addresses_frame.config(
            text=f"Все адреса ({visible_count}/{total_count}) - Сумма: {total_sum:.2f}, Вес: {total_weight:.3f}"
        )
        self.update_selected_count()

    def apply_filter(self, *args):
        """Применение фильтра без перезагрузки всех адресов"""
        filter_text = self.filter_var.get().lower()

        # Сохраняем текущую позицию прокрутки
        scroll_position = self.all_addresses_list.yview()

        # Очищаем список
        for item in self.all_addresses_list.get_children():
            self.all_addresses_list.delete(item)

        # Заполняем список с учетом фильтра
        for address in self.original_markers:
            if address not in self.selected_markers and address not in self.selected_markers2:  # Пропускаем избранные
                if not filter_text or filter_text in address.lower():
                    # Получаем значения суммы и веса для адреса
                    address_sum = self.address_values.get(address, {}).get('sum', 0.0)
                    address_weight = self.address_values.get(address, {}).get('weight', 0.0)

                    # Добавляем адрес в список с суммой и весом
                    self.all_addresses_list.insert('', 'end', values=(
                        address,
                        f"{address_sum:.2f}",
                        f"{address_weight:.3f}"
                    ))

        # Восстанавливаем позицию прокрутки
        self.all_addresses_list.yview_moveto(scroll_position[0])

        # Обновляем заголовок с количеством
        visible_count = len(self.all_addresses_list.get_children())
        total_count = len([a for a in self.original_markers if a not in self.selected_markers and a not in self.selected_markers2])
        self.update_address_count()

    def reset_filter(self):
        """Сброс фильтра без перезагрузки данных"""
        if self.filter_var.get():  # Только если фильтр не пустой
            self.filter_var.set("")
            # Не вызываем apply_filter, чтобы избежать двойной перезагрузки

    def delete_selected_failed(self):
        """Удаляет из необработанных с добавлением в удаленные"""
        selection = self.failed_addresses_list.curselection()
        if not selection:
            return

        for i in sorted(selection, reverse=True):
            address = self.failed_addresses_list.get(i)
            self.add_to_deleted(address)  # Добавляем в удаленные
            self.failed_addresses_list.delete(i)
            if address in self.failed_addresses:
                self.failed_addresses.remove(address)

    def delete_selected_from_all(self):
        """Удаляет выделенные адреса из основного списка"""
        selection = self.all_addresses_list.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите адреса для удаления")
            return

        # Получаем выбранные адреса
        selected_addresses = []
        for item_id in selection:
            item = self.all_addresses_list.item(item_id)
            address = item['values'][0]
            selected_addresses.append(address)

        # Удаляем из списка
        for item_id in selection:
            item = self.all_addresses_list.item(item_id)
            address = item['values'][0]
            self.all_addresses_list.delete(item_id)

            # Добавляем адрес в список удаленных
            self.add_to_deleted(address)

            # Удаляем маркер с карты и из всех хранилищ
            if address in self.markers:
                marker = self.markers.pop(address)  # Удаляем из общего хранилища

                # Физически удаляем маркер с карты
                if marker in self.map_widget.canvas_marker_list:
                    marker.delete()  # Удаляем с графического интерфейса
                    self.map_widget.canvas_marker_list.remove(marker)  # Удаляем из списка маркеров

                # Дополнительно вызываем delete() для гарантии
                try:
                    marker.delete()
                except:
                    pass

                # Удаляем из оригинального хранилища
                if address in self.original_markers:
                    self.original_markers.pop(address)

                # Удаляем из хранилища значений суммы и веса
                if address in self.address_values:
                    self.address_values.pop(address)

            # Если маркер был в избранном, удаляем его из соответствующего хранилища
            if address in self.selected_markers:
                self.selected_markers.pop(address)
            elif address in self.selected_markers2:
                self.selected_markers2.pop(address)

        # Дополнительная проверка - удаляем все маркеры, которые больше не нужны
        # но могли остаться на карте
        for marker in list(self.map_widget.canvas_marker_list):
            if (marker not in self.markers.values() and
                marker not in self.selected_markers.values() and
                marker not in self.selected_markers2.values()):
                try:
                    marker.delete()
                    self.map_widget.canvas_marker_list.remove(marker)
                except:
                    pass

        # Принудительное обновление карты
        self.map_widget.canvas.update()
        self.map_widget.update()

        messagebox.showinfo("Успех", f"Удалено адресов: {len(selected_addresses)}")
        self.update_address_count()

    def update_deleted_list(self):
        """Обновляет отображение списка удаленных адресов"""
        self.deleted_addresses_list.delete(0, tk.END)
        for addr in self.deleted_addresses:
            self.deleted_addresses_list.insert(tk.END, addr)


    def geocode_address(self, address, is_task_address=False):
        """Получение координат для адреса с использованием кэша и геокодеров

        Args:
            address: Адрес для геокодирования
            is_task_address: Флаг, указывающий, что адрес из задачи и не должен добавляться в основной список
        """
        if not address:
            return None, None

        # 1. Проверяем кэш
        if address in self.geocode_cache:
            print(f"Адрес '{address}' найден в кэше")
            return self.geocode_cache[address]

        # 2. Пробуем через Google Maps
        if self.gmaps_client:
            try:
                time.sleep(0.1)  # Ограничение запросов
                geocode_result = self.gmaps_client.geocode(address, region='ru', language='ru')

                if geocode_result:
                    location = geocode_result[0]['geometry']['location']
                    location_type = geocode_result[0]['geometry']['location_type']

                    # Принимаем только точные результаты
                    if location_type in ['ROOFTOP', 'RANGE_INTERPOLATED']:
                        lat, lng = location['lat'], location['lng']
                        # Проверяем, что координаты в пределах России
                        if 41.0 <= lat <= 81.0 and 19.0 <= lng <= 180.0:
                            # Всегда сохраняем в кэш для экономии запросов к API
                            self.geocode_cache[address] = (lat, lng)
                            if is_task_address:
                                print(f"Адрес задачи '{address}' геокодирован через Google и добавлен в кэш")
                            else:
                                print(f"Адрес '{address}' геокодирован через Google и добавлен в кэш")
                            return lat, lng
            except Exception as e:
                print(f"Ошибка Google Geocoding: {e}")

        # 3. Если Google не нашел, пробуем через Яндекс
        if hasattr(self, 'yandex_geocoder'):
            lat, lng = self.yandex_geocoder.geocode(address)
            if lat and lng:
                # Всегда сохраняем в кэш для экономии запросов к API
                self.geocode_cache[address] = (lat, lng)
                if is_task_address:
                    print(f"Адрес задачи '{address}' геокодирован через Яндекс и добавлен в кэш")
                else:
                    print(f"Адрес '{address}' геокодирован через Яндекс и добавлен в кэш")
                return lat, lng

        return None, None

    def load_excel(self):
        """Загрузка данных из Excel файла"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            print(f"Загрузка файла: {file_path}")
            # Проверяем, что файл существует и не поврежден
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Файл {file_path} не найден")

            # Проверяем размер файла
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError("Файл пуст")

            print(f"Размер файла: {file_size} байт")

            # Пробуем разные движки для чтения Excel
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
                print("Файл загружен с помощью openpyxl")
            except Exception as e1:
                print(f"Ошибка с openpyxl: {e1}")
                try:
                    df = pd.read_excel(file_path, engine='xlrd')
                    print("Файл загружен с помощью xlrd")
                except Exception as e2:
                    print(f"Ошибка с xlrd: {e2}")
                    raise Exception(f"Не удалось загрузить файл ни одним из движков. openpyxl: {e1}, xlrd: {e2}")

            print(f"Файл загружен. Столбцы: {df.columns.tolist()}")
            print(f"Количество строк: {len(df)}")
            print(f"Первые строки файла:\n{df.head()}")

            # Очищаем карту и списки
            self.clear_map()

            # Очищаем список адресов (Treeview)
            for item in self.all_addresses_list.get_children():
                self.all_addresses_list.delete(item)

            self.failed_addresses_list.delete(0, tk.END)
            self.failed_addresses = []

            # Очищаем хранилище значений адресов
            self.address_values.clear()

            # Сбрасываем общие значения суммы и веса
            self.list1_sum = 0.0
            self.list1_weight = 0.0
            self.list2_sum = 0.0
            self.list2_weight = 0.0

            progress_window = ttk.Toplevel(self.root)
            progress_window.title("Обработка адресов")
            progress_label = ttk.Label(progress_window, text="Получение координат...")
            progress_label.pack(pady=10)
            progress = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress.pack(pady=10)
            progress_window.update()

            total = len(df)
            processed = 0
            success_count = 0

            for _, row in df.iterrows():
                # Проверяем структуру файла и получаем адрес
                if 'Адрес разгрузки' in df.columns:
                    address = str(row['Адрес разгрузки']) if pd.notna(row['Адрес разгрузки']) else ""
                    print(f"Получен адрес из столбца 'Адрес разгрузки': {address}")
                else:
                    address = str(row.iloc[0]) if len(row) > 0 and pd.notna(row.iloc[0]) else ""
                    print(f"Получен адрес из первого столбца: {address}")

                if address and address.strip():
                    lat, lng = None, None
                    address_sum = 0.0
                    address_weight = 0.0

                    # Пробуем получить значения веса и суммы из вашего файла
                    # В вашем файле:
                    # Столбец 'Адрес разгрузки' - адрес
                    # Столбец 'Вес брутто (кг)' - вес
                    # Столбец 'Сумма с НДС' - сумма
                    try:
                        if 'Вес брутто (кг)' in df.columns and pd.notna(row['Вес брутто (кг)']):
                            address_weight = float(row['Вес брутто (кг)'])
                            print(f"Получен вес из столбца 'Вес брутто (кг)': {address_weight:.3f}")
                        elif len(row) > 1 and pd.notna(row.iloc[1]):
                            address_weight = float(row.iloc[1])
                            print(f"Получен вес из второго столбца: {address_weight:.3f}")

                        if 'Сумма с НДС' in df.columns and pd.notna(row['Сумма с НДС']):
                            address_sum = float(row['Сумма с НДС'])
                            print(f"Получена сумма из столбца 'Сумма с НДС': {address_sum:.2f}")
                        elif len(row) > 2 and pd.notna(row.iloc[2]):
                            address_sum = float(row.iloc[2])
                            print(f"Получена сумма из третьего столбца: {address_sum:.2f}")

                        print(f"Обработан адрес: {address}, вес: {address_weight:.3f}, сумма: {address_sum:.2f}")
                    except (ValueError, TypeError) as e:
                        print(f"Ошибка при обработке значений для адреса {address}: {e}")
                        address_weight = 0.0
                        address_sum = 0.0

                    # Координаты будем получать через геокодирование
                    lat, lng = None, None

                    # Если координат нет в файле или они некорректны, геокодируем
                    if lat is None or lng is None or not self.validate_coordinates(lat, lng):
                        print(f"Геокодирование адреса: {address}")
                        lat, lng = self.geocode_address(address)
                        if lat is not None and lng is not None:
                            print(f"Успешно геокодирован: {address} -> {lat}, {lng}")
                        else:
                            print(f"Не удалось геокодировать: {address}")

                        # Если Яндекс нашел, но Google нет - помечаем желтым
                        if lat and lng and not self.gmaps_client:
                            marker_color = "yellow"
                        else:
                            marker_color = None
                    else:
                        marker_color = None

                    if lat is not None and lng is not None:
                        # Используем коричневый цвет для маркеров (как было раньше)
                        marker = self.map_widget.set_marker(
                            lat, lng,
                            text=address,
                            marker_color_circle="brown",
                            marker_color_outside="brown"
                        )

                        self.markers[address] = marker
                        self.original_markers[address] = marker

                        # Сохраняем значения суммы и веса для адреса
                        self.address_values[address] = {
                            'sum': address_sum,
                            'weight': address_weight
                        }

                        # Добавляем адрес в список с суммой и весом
                        self.all_addresses_list.insert('', 'end', values=(
                            address,
                            f"{address_sum:.2f}",
                            f"{address_weight:.3f}"
                        ))
                        success_count += 1
                    else:
                        self.process_failed_address(address)

                processed += 1
                progress['value'] = (processed / total) * 100
                progress_label.config(text=f"Обработано: {processed}/{total} | Успешно: {success_count}")
                progress_window.update()

            progress_window.destroy()

            if self.markers:
                first_marker = next(iter(self.markers.values()))
                self.map_widget.set_position(first_marker.position[0], first_marker.position[1])

            # Подсчитываем общую сумму и вес загруженных адресов
            total_sum = sum(info['sum'] for info in self.address_values.values())
            total_weight = sum(info['weight'] for info in self.address_values.values())

            self.show_modern_message("Результат загрузки",
                              f"Успешно обработано: {success_count}\n"
                              f"Не удалось обработать: {len(self.failed_addresses)}\n"
                              f"Общая сумма: {total_sum:.2f}\n"
                              f"Общий вес: {total_weight:.3f}",
                              "success")

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось загрузить файл:\n{str(e)}")

        # Применяем фильтр и обновляем счетчики
        self.apply_filter()
        self.update_address_count()

    def clear_map(self):
        """Очистка карты и всех хранилищ"""
        print("ОТЛАДКА: Вызван clear_map() - очищаем карту, но НЕ расписание")

        # Удаляем все маркеры с карты
        for marker in list(self.markers.values()):
            marker.delete()

        # Очищаем все хранилища
        self.markers.clear()
        self.original_markers.clear()
        self.selected_markers.clear()
        self.selected_markers2.clear()

        # Очищаем хранилище значений адресов
        self.address_values.clear()

        # Сбрасываем общие значения суммы и веса
        self.list1_sum = 0.0
        self.list1_weight = 0.0
        self.list2_sum = 0.0
        self.list2_weight = 0.0

        # Очищаем списки
        for item in self.all_addresses_list.get_children():
            self.all_addresses_list.delete(item)
        self.selected_addresses_list.delete(0, tk.END)
        self.selected_addresses_list2.delete(0, tk.END)

        # Обновляем счетчики
        self.update_counts()

        # Принудительное обновление карты
        self.map_widget.canvas.update()
        self.map_widget.update()

        print("ОТЛАДКА: clear_map() завершен - расписание НЕ затронуто")

    def suggest_similar_addresses(self, address):
        """Получение похожих вариантов адреса через Яндекс"""
        if not hasattr(self, 'yandex_geocoder'):
            return []

        url = "https://suggest-maps.yandex.ru/v1/suggest"
        params = {
            "apikey": self.YANDEX_MAPS_API_KEY,
            "text": address,
            "type": "geo",
            "lang": "ru_RU"
        }

        try:
            response = requests.get(url, params=params).json()
            return [item["display_name"] for item in response.get("results", [])[:5]]  # первые 5 вариантов
        except Exception as e:
            print(f"Ошибка подсказок Яндекс: {e}")
            return []

    def process_failed_address(self, address):
        """Обработка не найденных адресов с предложением вариантов"""
        # Пробуем найти похожие адреса
        suggestions = self.suggest_similar_addresses(address)

        if not suggestions:
            self.failed_addresses.append(address)
            self.failed_addresses_list.insert(tk.END, address)
            return

        # Создаем диалог с вариантами
        dialog = ttk.Toplevel(self.root)
        dialog.title("Выберите вариант адреса")

        ttk.Label(dialog, text=f"Точный адрес '{address}' не найден. Возможно вы имели в виду:").pack(pady=10)

        listbox = tk.Listbox(dialog, width=80, height=5)
        listbox.pack(pady=5)

        for suggestion in suggestions:
            listbox.insert(tk.END, suggestion)

        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_address = listbox.get(selection[0])
                dialog.destroy()
                # Пробуем геокодировать выбранный вариант
                lat, lng = self.geocode_address(selected_address)
                if lat and lng:
                    marker = self.map_widget.set_marker(lat, lng, text=selected_address)
                    self.markers[selected_address] = marker
                    self.original_markers[selected_address] = marker

                    # Добавляем адрес в список с нулевыми значениями суммы и веса
                    self.address_values[selected_address] = {'sum': 0.0, 'weight': 0.0}
                    self.all_addresses_list.insert('', 'end', values=(selected_address, "0.00", "0.000"))
                else:
                    self.failed_addresses.append(address)
                    self.failed_addresses_list.insert(tk.END, address)

        ttk.Button(dialog, text="Выбрать", command=on_select).pack(pady=5)
        ttk.Button(dialog, text="Пропустить", command=lambda: [dialog.destroy(),
            self.failed_addresses_list.insert(tk.END, address)]).pack(pady=5)

    def validate_coordinates(self, lat, lng):
        """Проверка корректности координат"""
        try:
            lat = float(lat)
            lng = float(lng)
            return -90 <= lat <= 90 and -180 <= lng <= 180
        except (ValueError, TypeError):
            return False

    def add_manual(self):
        """Добавление точки с ручным вводом координат"""
        address = self.address_entry.get().strip()
        lat = self.lat_entry.get().strip()
        lng = self.lng_entry.get().strip()

        if not address:
            messagebox.showwarning("Предупреждение", "Введите адрес")
            return

        # Если координаты введены - проверяем их
        if lat and lng:
            if not self.is_valid_coordinate(lat) or not self.is_valid_coordinate(lng):
                messagebox.showerror("Ошибка", "Введите корректные координаты")
                return
            lat = float(lat)
            lng = float(lng)
        else:
            # Если координаты не введены - геокодируем адрес
            lat, lng = self.geocode_address(address)
            if lat is None or lng is None:
                messagebox.showerror("Ошибка", "Не удалось определить координаты для адреса")
                self.failed_addresses.append(address)
                self.failed_addresses_list.insert(tk.END, address)
                return

        # Определяем цвет маркера
        marker_color = None
        if address in self.selected_markers:
            marker_color = "blue"
        elif address in self.selected_markers2:
            marker_color = "green"

        # Добавляем маркер на карту
        if address in self.markers:
            self.markers[address].delete()

        marker = self.map_widget.set_marker(
            lat, lng,
            text=address,
            marker_color_circle=marker_color,
            marker_color_outside=marker_color,
            text_color=marker_color
        )

        # Обновляем хранилища
        self.markers[address] = marker
        self.original_markers[address] = marker

        # Добавляем в основной список, если не в избранном
        if address not in self.selected_markers and address not in self.selected_markers2:
            # Добавляем адрес в список с нулевыми значениями суммы и веса
            self.address_values[address] = {'sum': 0.0, 'weight': 0.0}
            self.all_addresses_list.insert('', 'end', values=(address, "0.00", "0.000"))

        # Центрируем карту на новом маркере
        self.map_widget.set_position(lat, lng)

        # Очищаем поля ввода
        self.address_entry.delete(0, tk.END)
        self.lat_entry.delete(0, tk.END)
        self.lng_entry.delete(0, tk.END)

        self.update_address_count()

    def change_map_type(self):
        """Изменение типа карты"""
        map_type = self.map_type.get()
        if map_type == "normal":
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=m&hl=en&x={x}&y={y}&z={z}")
        elif map_type == "satellite":
            self.map_widget.set_tile_server("https://mt0.google.com/vt/lyrs=s&hl=en&x={x}&y={y}&z={z}")

    def save_selected_with_driver(self):
        """Сохранение выбранного списка с водителем с выбором региона"""
        if self.list_selector.get() == "list1":
            selected_dict = self.selected_markers
            list_widget = self.selected_addresses_list
            marker_color = "blue"
        else:
            selected_dict = self.selected_markers2
            list_widget = self.selected_addresses_list2
            marker_color = "green"

        if not selected_dict:
            messagebox.showwarning("Предупреждение", "Нет адресов в выбранном списке")
            return

        driver = self.driver_var.get()
        if not driver:
            self.show_modern_message("Выберите водителя", "Выберите водителя для сохранения адресов", "warning")
            return

        # Всегда выбираем новый уникальный цвет для водителя (не используем цвета списков)
        marker_color = self.get_next_available_color()
        print(f"Назначен цвет {marker_color} для водителя {driver}")

        # Проверяем, есть ли уже такой водитель в списке (ищем по имени водителя)
        driver_exists = False
        if hasattr(self, 'saved_drivers'):
            for key, driver_info in self.saved_drivers.items():
                if driver_info.get('driver_name', key) == driver:
                    driver_exists = True
                    break

        if driver_exists:
            # Показываем информационное сообщение, но продолжаем сохранение в выбранный регион
            self.show_modern_message("Водитель уже назначен",
                               f"Водитель {driver} уже есть в расписании в другом регионе.\n"
                               f"Данные будут сохранены только в выбранный регион.", "warning")

            # НЕ удаляем старый цвет из списка используемых, так как он может использоваться в другом регионе

        # Получаем текущий день недели из вкладки расписания
        day = self.day_var.get()

        # Получаем список регионов для выбранного дня с дополнительной информацией
        regions_info = []
        for i, entry in enumerate(self.drivers_schedule[day]):
            region_name = entry['region']
            current_driver = entry.get('driver', '')

            # Проверяем, есть ли уже сохраненные данные для этого водителя в этом регионе
            has_data = False
            count = 0
            if current_driver and hasattr(self, 'saved_drivers'):
                for key, driver_info in self.saved_drivers.items():
                    if (driver_info.get('driver_name', key) == current_driver and
                        driver_info.get('region') == region_name):
                        has_data = driver_info.get('count', 0) > 0
                        count = driver_info.get('count', 0)
                        break

            # Формируем отображаемое название
            display_name = region_name
            if has_data:
                display_name += f" (водитель: {current_driver}, адресов: {count})"
            elif current_driver:
                display_name += f" (водитель: {current_driver})"
            else:
                display_name += " (свободно)"

            regions_info.append({
                'index': i,
                'region': region_name,
                'display_name': display_name,
                'driver': current_driver,
                'has_data': has_data
            })

        # Если нет регионов, предупреждаем пользователя
        if not regions_info:
            messagebox.showwarning("Предупреждение", f"Нет регионов в расписании на {day}")
            return

        # Создаем диалоговое окно для выбора региона
        region_dialog = ttk.Toplevel(self.root)
        region_dialog.title("Выберите регион")
        region_dialog.geometry("600x500")  # Увеличиваем высоту окна еще больше
        region_dialog.transient(self.root)
        region_dialog.grab_set()

        # Центрируем окно на экране
        self.center_window(region_dialog)

        # Создаем фрейм для списка регионов
        region_frame = ttk.Frame(region_dialog, padding=10)
        region_frame.pack(fill=tk.BOTH, expand=True)

        # Метка с инструкцией
        ttk.Label(region_frame, text=f"Выберите регион для водителя {driver} на {day}:",
                 font=("Segoe UI", 10)).pack(pady=5)

        # Настраиваем стиль для списка регионов
        self.style.configure("RegionSelect.Treeview",
                           background="#2b3e50",  # Темно-синий фон
                           foreground="white",    # Белый текст
                           fieldbackground="#2b3e50",  # Фон полей
                           font=("Segoe UI", 11),  # Увеличенный шрифт
                           rowheight=30)  # Увеличенная высота строки

        self.style.configure("RegionSelect.Treeview.Heading",
                           background="#34495e",  # Менее яркий серо-синий для заголовков
                           foreground="#ecf0f1",  # Светло-серый текст заголовков
                           font=("Segoe UI", 10, "bold"))  # Жирный шрифт для заголовков

        # Создаем Treeview для списка регионов
        region_tree = ttk.Treeview(region_frame, columns=("region",), show="headings",
                                  style="RegionSelect.Treeview", height=10)
        region_tree.heading("region", text="Регион")
        region_tree.column("region", width=550)
        region_tree.pack(fill=tk.BOTH, expand=True, pady=5)

        # Добавляем скроллбар
        region_scrollbar = ttk.Scrollbar(region_tree, orient="vertical", command=region_tree.yview)
        region_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        region_tree.configure(yscrollcommand=region_scrollbar.set)

        # Добавляем регионы в список с дополнительной информацией
        for i, region_info in enumerate(regions_info):
            region_tree.insert('', 'end', iid=str(i), values=(region_info['display_name'],))

        # Выбираем первый регион по умолчанию
        if regions_info:
            region_tree.selection_set("0")
            region_tree.focus("0")

        # Переменная для хранения выбранного региона
        selected_region_info = [None]

        # Функция для сохранения выбранного региона
        def confirm_region():
            selection = region_tree.selection()
            if selection:
                selected_index = int(selection[0])
                selected_region_info[0] = regions_info[selected_index]
                region_dialog.destroy()
            else:
                self.show_modern_message("Выберите регион", "Выберите регион из списка", "warning")

        # Кнопки подтверждения и отмены
        button_frame = ttk.Frame(region_dialog)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(button_frame, text="Подтвердить", command=confirm_region).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Отмена", command=region_dialog.destroy).pack(side=tk.RIGHT, padx=5)

        # Ждем, пока пользователь выберет регион
        self.root.wait_window(region_dialog)

        # Если пользователь отменил выбор, прерываем операцию
        if selected_region_info[0] is None:
            return

        # Сохраняем информацию о выбранном регионе
        selected_region_info = selected_region_info[0]
        selected_region = selected_region_info['region']
        selected_region_index = selected_region_info['index']

        default_filename = f"маршруты_{datetime.now().strftime('%Y-%m-%d')}.xlsx"
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            initialfile=default_filename,
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if not file_path:
            return

        try:
            # Создаем DataFrame с данными
            data = []
            # Добавляем заголовки столбцов
            data.append({
                'Адрес': "Адрес",
                'Широта': "Широта",
                'Долгота': "Долгота"
            })
            # Добавляем пустую строку
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем строку с ФИО водителя
            data.append({
                'Адрес': f"Водитель: {driver}",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем пустую строку после водителя
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            # Добавляем сами адреса
            for address, marker in selected_dict.items():
                data.append({
                    'Адрес': address,
                    'Широта': marker.position[0],
                    'Долгота': marker.position[1]
                })
            # Добавляем пустую строку в конце
            data.append({
                'Адрес': "",
                'Широта': "",
                'Долгота': ""
            })
            df = pd.DataFrame(data)

            # Проверяем, существует ли файл
            if os.path.exists(file_path):
                existing_df = pd.read_excel(file_path)
                # Находим все индексы строк с водителями
                driver_indices = []
                for i, row in existing_df.iterrows():
                    if isinstance(row['Адрес'], str) and row['Адрес'].startswith('Водитель:'):
                        driver_indices.append(i)
                if driver_indices:
                    # Если есть водители, добавляем после последнего
                    last_driver_index = driver_indices[-1]
                    # Находим следующую пустую строку после последнего водителя
                    next_empty_index = last_driver_index + 1
                    while next_empty_index < len(existing_df) and existing_df.iloc[next_empty_index]['Адрес'] != "":
                        next_empty_index += 1
                    # Вставляем новые данные после последнего водителя
                    part1 = existing_df.iloc[:next_empty_index+1]
                    part2 = existing_df.iloc[next_empty_index+1:]
                    df = pd.concat([part1, df.iloc[2:]], ignore_index=True)
                else:
                    # Если водителей нет, добавляем в конец
                    df = pd.concat([existing_df, df.iloc[1:]], ignore_index=True)

            # Сохраняем файл с форматированием
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False)
                # Настраиваем ширину столбцов
                worksheet = writer.sheets['Sheet1']
                worksheet.column_dimensions['A'].width = 50  # Адрес
                worksheet.column_dimensions['B'].width = 15  # Широта
                worksheet.column_dimensions['C'].width = 15  # Долгота

            # Сохраняем адреса и координаты перед удалением
            addresses_to_remove = list(selected_dict.keys())
            markers_to_remove = list(selected_dict.values())

            # Создаем список адресов для сохранения в список водителей
            addresses_list = []
            coordinates_list = []

            # Сохраняем адреса и их координаты
            for address, marker in selected_dict.items():
                addresses_list.append(address)
                # Сохраняем координаты в кэш, если их там еще нет
                if address not in self.geocode_cache and hasattr(marker, 'position'):
                    # Убедимся, что сохраняем координаты в формате кортежа (lat, lng)
                    try:
                        lat, lng = marker.position[0], marker.position[1]
                        self.geocode_cache[address] = (lat, lng)
                    except (TypeError, IndexError):
                        # Если не удалось получить координаты, пропускаем
                        pass

            # Добавляем водителя в список сохраненных с указанием региона и его индекса
            # Создаем уникальный ключ для этой комбинации водитель+регион+день
            day = self.day_var.get()
            driver_key = f"{driver}_{selected_region}_{day}_{selected_region_index}"
            self.add_driver_to_list(driver, addresses_list, marker_color, selected_region, selected_region_index, driver_key)

            # Принудительно обновляем отображение расписания после сохранения
            self.update_schedule_display()

            # Сохраняем данные водителей
            self.save_drivers_addresses()

            # Сначала удаляем маркеры из всех словарей
            for address in addresses_to_remove:
                # Удаляем из словаря выбранных маркеров
                marker = selected_dict.pop(address, None)

                # НЕ удаляем маркер с карты и НЕ меняем его цвет!
                # Маркер должен остаться на карте с тем же цветом для визуального контроля
                if marker:
                    # Обновляем ссылки в основных словарях (маркер остается тот же)
                    self.markers[address] = marker
                    self.original_markers[address] = marker

                    # НЕ добавляем адрес обратно в основной список!
                    # Адреса, которые были сохранены с водителем, не должны появляться в основном списке

                    print(f"✅ Адрес '{address}' сохранен с водителем (маркер остается на карте с цветом {marker_color})")
                    print(f"🚫 Адрес НЕ возвращается в основной список - он уже назначен водителю")

            # НЕ удаляем маркеры с карты - они должны остаться для визуального контроля!
            # Только обновляем карту
            self.map_widget.update()
            # Очищаем список
            list_widget.delete(0, tk.END)
            self.driver_var.set('')
            self.update_counts()

            self.show_modern_message("Успешно сохранено", f"Сохранено {len(data)-5} адресов для водителя {driver}", "success")
            # Принудительное обновление карты
            self.map_widget.update()

            # Проверяем состояние распределения после сохранения
            self.check_distribution_after_save()

        except Exception as e:
            messagebox.showerror("Ошибка", f"Не удалось сохранить файл:{str(e)}")

    def load_geocode_cache(self):
        """Загрузка кэша геокодированных адресов из Excel файла"""
        cache_file = "geocode_cache.xlsx"

        if not os.path.exists(cache_file):
            print("Файл кэша не найден, будет создан новый")
            return

        try:
            df = pd.read_excel(cache_file)

            # Проверяем наличие необходимых столбцов
            if all(col in df.columns for col in ['Адрес', 'Широта', 'Долгота']):
                for _, row in df.iterrows():
                    address = row['Адрес']
                    lat = row['Широта']
                    lng = row['Долгота']

                    if pd.notna(address) and pd.notna(lat) and pd.notna(lng):
                        self.geocode_cache[address] = (float(lat), float(lng))

                print(f"Загружено {len(self.geocode_cache)} адресов из кэша")
            else:
                print("Некорректный формат файла кэша")
        except Exception as e:
            print(f"Ошибка при загрузке кэша: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить кэш адресов:\n{str(e)}")

    def save_geocode_cache(self):
        """Сохранение кэша геокодированных адресов в Excel файл"""
        cache_file = "geocode_cache.xlsx"

        try:
            # Создаем DataFrame из кэша
            data = []
            for address, (lat, lng) in self.geocode_cache.items():
                data.append({
                    'Адрес': address,
                    'Широта': lat,
                    'Долгота': lng
                })

            if not data:
                print("Кэш пуст, нечего сохранять")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(cache_file, index=False)
            print(f"Сохранено {len(data)} адресов в кэш")
            # Убираем сообщение при закрытии программы - адреса сохраняются автоматически
        except Exception as e:
            print(f"Ошибка при сохранении кэша: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить кэш адресов:\n{str(e)}")

    def get_next_available_color(self):
        """Возвращает следующий доступный цвет для маркеров"""
        print(f"\n🎨 === ВЫБОР ЦВЕТА ===")

        # Собираем все используемые цвета из разных источников
        all_used_colors = set()

        # 1. Добавляем зарезервированные цвета
        reserved_colors = {"blue", "green", "brown", "purple"}
        all_used_colors.update(reserved_colors)
        print(f"🔒 Зарезервированные цвета: {sorted(reserved_colors)}")

        # 2. Добавляем цвета текущей сессии распределения (НЕ из расписания)
        current_session_colors = set()
        if hasattr(self, 'current_session_colors'):
            current_session_colors.update(self.current_session_colors)
        all_used_colors.update(current_session_colors)
        print(f"📝 Цвета текущей сессии: {sorted(current_session_colors)}")

        # 3. Добавляем цвета из сохраненных водителей в расписании
        saved_colors = set()
        if hasattr(self, 'saved_drivers'):
            for key, driver_info in self.saved_drivers.items():
                if 'color' in driver_info and driver_info['color']:
                    saved_colors.add(driver_info['color'])
        all_used_colors.update(saved_colors)
        print(f"💾 Цвета из сохраненных водителей: {sorted(saved_colors)}")

        # 4. Добавляем цвета из ВСЕХ маркеров на карте (включая сохраненные с водителями)
        marker_colors = set()
        if hasattr(self, 'markers'):
            for address, marker in self.markers.items():
                try:
                    # Пытаемся получить цвет маркера разными способами
                    if hasattr(marker, 'marker_color_circle') and marker.marker_color_circle:
                        marker_colors.add(marker.marker_color_circle)
                        print(f"    🎨 Маркер {address}: цвет {marker.marker_color_circle}")
                    elif hasattr(marker, 'color') and marker.color:
                        marker_colors.add(marker.color)
                        print(f"    🎨 Маркер {address}: цвет {marker.color}")
                except Exception as e:
                    print(f"    ❌ Ошибка получения цвета маркера {address}: {e}")
        all_used_colors.update(marker_colors)
        print(f"🗺️ Цвета из маркеров на карте: {sorted(marker_colors)}")

        # 4.1. Дополнительная проверка через canvas_marker_list карты
        canvas_marker_colors = set()
        if hasattr(self, 'map_widget') and hasattr(self.map_widget, 'canvas_marker_list'):
            for canvas_marker in self.map_widget.canvas_marker_list:
                try:
                    if hasattr(canvas_marker, 'marker_color_circle') and canvas_marker.marker_color_circle:
                        canvas_marker_colors.add(canvas_marker.marker_color_circle)
                        print(f"    🖼️ Canvas маркер: цвет {canvas_marker.marker_color_circle}")
                except Exception as e:
                    print(f"    ❌ Ошибка получения цвета canvas маркера: {e}")
        all_used_colors.update(canvas_marker_colors)
        print(f"🖼️ Цвета из canvas маркеров: {sorted(canvas_marker_colors)}")

        # 5. Добавляем цвета из выбранных маркеров (список 1)
        selected_colors = set()
        if hasattr(self, 'selected_markers'):
            for address, marker in self.selected_markers.items():
                try:
                    if hasattr(marker, 'marker_color_circle'):
                        selected_colors.add(marker.marker_color_circle)
                    elif hasattr(marker, 'color'):
                        selected_colors.add(marker.color)
                except:
                    pass
        all_used_colors.update(selected_colors)
        print(f"✅ Цвета из выбранных маркеров: {sorted(selected_colors)}")

        # 6. Добавляем цвета из списка 2
        list2_colors = set()
        if hasattr(self, 'selected_markers2'):
            for address, marker in self.selected_markers2.items():
                try:
                    if hasattr(marker, 'marker_color_circle'):
                        list2_colors.add(marker.marker_color_circle)
                    elif hasattr(marker, 'color'):
                        list2_colors.add(marker.color)
                except:
                    pass
        all_used_colors.update(list2_colors)
        print(f"📋 Цвета из списка 2: {sorted(list2_colors)}")

        print(f"🎨 ВСЕ используемые цвета: {sorted(all_used_colors)}")
        print(f"🌈 Доступные цвета: {self.available_colors}")

        # Ищем первый доступный цвет
        for color in self.available_colors:
            if color not in all_used_colors:
                # НЕ добавляем в used_colors здесь, это будет сделано при фактическом использовании
                print(f"✅ ВЫБРАН новый цвет: {color}")
                print(f"🎨 === КОНЕЦ ВЫБОРА ЦВЕТА ===\n")
                return color

        # Если все цвета использованы, подсчитываем частоту использования
        print(f"⚠️ Все цвета использованы, подсчитываем частоту...")
        color_usage = {}

        # Инициализируем счетчики
        for color in self.available_colors:
            color_usage[color] = 0

        # Подсчитываем использование каждого цвета (исключая зарезервированные)
        for color in all_used_colors:
            if color in self.available_colors and color not in reserved_colors:
                color_usage[color] += 1

        print(f"📊 Частота использования цветов: {color_usage}")

        # Находим цвет с минимальным использованием (исключая зарезервированные)
        available_for_reuse = {k: v for k, v in color_usage.items() if k not in reserved_colors}
        if available_for_reuse:
            min_usage_color = min(available_for_reuse.items(), key=lambda x: x[1])[0]
            print(f"⚠️ ПОВТОРНО назначен наименее используемый цвет: {min_usage_color} (использован {available_for_reuse[min_usage_color]} раз)")
        else:
            # Крайний случай - берем первый доступный
            min_usage_color = self.available_colors[0]
            print(f"🚨 КРАЙНИЙ СЛУЧАЙ: назначен первый доступный цвет: {min_usage_color}")

        print(f"🎨 === КОНЕЦ ВЫБОРА ЦВЕТА ===\n")
        return min_usage_color

    def initialize_used_colors(self):
        """Инициализирует список используемых цветов на основе сохраненных водителей"""
        self.used_colors = []

        # Добавляем цвета из сохраненных водителей в расписании
        if hasattr(self, 'saved_drivers'):
            for key, driver_info in self.saved_drivers.items():
                if 'color' in driver_info and driver_info['color']:
                    color = driver_info['color']
                    if color not in self.used_colors:
                        self.used_colors.append(color)
                        driver_name = driver_info.get('driver_name', key)
                        print(f"Инициализирован используемый цвет: {color} для водителя {driver_name}")

        print(f"Инициализировано {len(self.used_colors)} используемых цветов: {self.used_colors}")

    def add_driver_to_list(self, driver_name, addresses, color, region=None, region_index=None, driver_key=None):
        """Добавляет водителя и его адреса в список сохраненных"""
        # Вычисляем сумму и вес для адресов
        total_sum = sum(self.address_values.get(addr, {}).get('sum', 0.0) for addr in addresses)
        total_weight = sum(self.address_values.get(addr, {}).get('weight', 0.0) for addr in addresses)

        # Используем уникальный ключ, если он предоставлен, иначе имя водителя
        key = driver_key if driver_key else driver_name

        # Сохраняем информацию о водителе с уникальным ключом
        self.saved_drivers[key] = {
            'driver_name': driver_name,  # Сохраняем оригинальное имя водителя
            'addresses': addresses,
            'color': color,
            'count': len(addresses),
            'region': region,
            'region_index': region_index,
            'sum': total_sum,
            'weight': total_weight
        }

        # Обновляем расписание, если указан регион
        if region and region_index is not None:
            self.update_driver_in_schedule(driver_name, region, region_index)

    def update_driver_in_schedule(self, driver_name, region, region_index):
        """Обновляет информацию о водителе в расписании"""
        # Получаем текущий день недели из вкладки расписания
        day = self.day_var.get()

        # Используем индекс региона для точного обновления нужной записи
        if 0 <= region_index < len(self.drivers_schedule[day]):
            self.drivers_schedule[day][region_index]['driver'] = driver_name
            print(f"Обновлен водитель '{driver_name}' для региона '{region}' (индекс {region_index}) на {day}")
        else:
            print(f"Ошибка: неверный индекс региона {region_index} для дня {day}")

        # Обновляем отображение расписания
        self.update_schedule_display()

        # Сохраняем расписание
        self.save_schedule()

    def clear_schedule_entry_data(self):
        """Очищает данные выбранной записи в расписании (водитель, адреса, сумма), оставляя только регион"""
        # Получаем выбранную запись
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите запись в расписании для очистки")
            return

        # Получаем данные выбранной записи
        item = self.schedule_tree.item(selection[0])
        values = item['values']

        if len(values) < 2:
            messagebox.showwarning("Предупреждение", "Некорректный формат записи")
            return

        region = values[0]
        current_driver = values[1] if len(values) > 1 else ""

        # Если водитель не назначен, нечего очищать
        if not current_driver:
            messagebox.showinfo("Информация", f"В регионе '{region}' нет назначенного водителя")
            return

        # Подтверждение очистки
        if not messagebox.askyesno("Подтверждение",
                                 f"Очистить данные для региона '{region}'?\n"
                                 f"Водитель: {current_driver}\n\n"
                                 f"Будут удалены:\n"
                                 f"- Назначение водителя\n"
                                 f"- Сохраненные адреса\n"
                                 f"- Данные о количестве и сумме\n\n"
                                 f"Регион останется в расписании."):
            return

        # Получаем текущий день
        day = self.day_var.get()

        # Находим индекс записи в расписании
        region_index = None
        for i, entry in enumerate(self.drivers_schedule[day]):
            if entry['region'] == region and entry.get('driver', '') == current_driver:
                region_index = i
                break

        if region_index is None:
            messagebox.showerror("Ошибка", "Не удалось найти запись в расписании")
            return

        # Очищаем водителя из расписания
        self.drivers_schedule[day][region_index]['driver'] = ''

        # Удаляем сохраненные данные водителя для этого региона
        if hasattr(self, 'saved_drivers'):
            keys_to_remove = []
            for key, driver_info in self.saved_drivers.items():
                if (driver_info.get('driver_name', key) == current_driver and
                    driver_info.get('region') == region):
                    keys_to_remove.append(key)

            # Удаляем найденные записи
            for key in keys_to_remove:
                del self.saved_drivers[key]
                print(f"ОТЛАДКА: Удалены данные водителя {current_driver} для региона {region} (ключ: {key})")

            print(f"ОТЛАДКА: После очистки записи осталось {len(self.saved_drivers)} водителей в saved_drivers")

        # Обновляем отображение расписания
        self.update_schedule_display()

        # Сохраняем расписание
        self.save_schedule()

        messagebox.showinfo("Успех", f"Данные для региона '{region}' очищены.\nВодитель {current_driver} удален из назначения.")

    # Методы restore_driver_addresses и delete_driver удалены, так как их функциональность перенесена в список с расписанием

    # Методы load_drivers_list и save_drivers_list удалены, так как их функциональность перенесена в список с расписанием

    def add_task(self, event=None):
        """Добавляет новую задачу в список"""
        task_text = self.task_entry.get().strip()
        if not task_text:
            return

        # Получаем текущую дату
        current_date = datetime.now().strftime("%d.%m.%Y")

        # Создаем новую задачу
        task_id = self.next_task_id
        self.tasks[task_id] = {
            'date': current_date,
            'task': task_text,
            'driver': '',
            'description': '',
            'completed': False,
            'address': '',  # Поле для адреса
            'contractor': ''  # Поле для контрагента
        }

        # Увеличиваем счетчик ID
        self.next_task_id += 1

        # Очищаем поле ввода
        self.task_entry.delete(0, tk.END)

        # Обновляем заголовок фрейма
        self.tasks_frame.config(text=f"📋 Управление задачами ({len(self.tasks)})")

        # Обновляем отображение с сортировкой
        self.update_tasks_display()

    def update_tasks_display(self):
        """Обновляет отображение списка задач с сортировкой (невыполненные вверху, выполненные внизу)"""
        # Очищаем текущий список
        for item in self.tasks_list.get_children():
            self.tasks_list.delete(item)

        # Создаем список задач для сортировки
        tasks_for_sorting = []
        for task_id, task_info in self.tasks.items():
            tasks_for_sorting.append((task_id, task_info))

        # Сортируем: сначала по статусу выполнения (False первые), затем по дате
        tasks_for_sorting.sort(key=lambda x: (x[1].get('completed', False), x[1].get('date', '')))

        # Добавляем задачи в отсортированном порядке
        for task_id, task_info in tasks_for_sorting:
            date = task_info.get('date', '')
            task = task_info.get('task', '')
            driver = task_info.get('driver', '')
            completed = task_info.get('completed', False)

            # Определяем иконку статуса
            status_icon = "✅" if completed else "🔄"

            # Добавляем задачу в список с новой структурой столбцов
            self.tasks_list.insert('', 'end', iid=str(task_id), values=(status_icon, date, task, driver))

            # Настраиваем стили для выполненных и невыполненных задач
            if completed:
                # Создаем тег для выполненных задач, если его еще нет
                if not hasattr(self, 'completed_tag_created'):
                    self.tasks_list.tag_configure('completed',
                                                 font=('Segoe UI', 10, 'overstrike'),
                                                 foreground='#adb5bd')  # Светло-серый цвет для выполненных
                    self.completed_tag_created = True

                # Применяем тег к задаче
                self.tasks_list.item(str(task_id), tags=('completed',))
            else:
                # Создаем тег для активных задач, если его еще нет
                if not hasattr(self, 'active_tag_created'):
                    self.tasks_list.tag_configure('active',
                                                 font=('Segoe UI', 10, 'normal'),
                                                 foreground='white')  # Белый цвет для активных задач
                    self.active_tag_created = True

                # Применяем тег к активной задаче
                self.tasks_list.item(str(task_id), tags=('active',))

        # Обновляем статистику
        self.update_tasks_statistics()

    def update_tasks_statistics(self):
        """Обновляет статистику задач"""
        if not hasattr(self, 'stats_label'):
            return

        total_tasks = len(self.tasks)
        completed_tasks = sum(1 for task in self.tasks.values() if task.get('completed', False))
        active_tasks = total_tasks - completed_tasks

        stats_text = f"📊 Всего: {total_tasks} | 🔄 Активных: {active_tasks} | ✅ Выполнено: {completed_tasks}"
        self.stats_label.config(text=stats_text)

    def show_tasks_context_menu(self, event):
        """Показывает контекстное меню для списка задач"""
        # Определяем, на какой элемент кликнули
        item = self.tasks_list.identify_row(event.y)
        if item:
            # Выбираем элемент
            self.tasks_list.selection_set(item)

            # Создаем контекстное меню
            context_menu = tk.Menu(self.root, tearoff=0)

            # Получаем информацию о задаче
            task_id = int(item)
            task_info = self.tasks.get(task_id, {})
            completed = task_info.get('completed', False)

            # Добавляем пункты меню
            context_menu.add_command(
                label="✏️ Редактировать",
                command=self.edit_task
            )
            context_menu.add_separator()

            if completed:
                context_menu.add_command(
                    label="🔄 Отметить как невыполненную",
                    command=lambda: self.toggle_task_completion(task_id)
                )
            else:
                context_menu.add_command(
                    label="✅ Отметить как выполненную",
                    command=lambda: self.toggle_task_completion(task_id)
                )

            context_menu.add_separator()
            context_menu.add_command(
                label="🗑️ Удалить",
                command=self.delete_task
            )

            # Показываем меню
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def toggle_task_completion(self, task_id):
        """Переключает статус выполнения задачи"""
        if task_id not in self.tasks:
            return

        # Переключаем статус
        current_status = self.tasks[task_id].get('completed', False)
        self.tasks[task_id]['completed'] = not current_status

        # Если задача отмечается как выполненная, убираем её маркер с карты
        if not current_status:  # Если становится выполненной
            address = self.tasks[task_id].get('address', '')
            if address:
                print(f"Задача отмечена как выполненная, удаляем маркер для адреса '{address}'")
                self.remove_task_address_from_map(address)
        else:  # Если становится невыполненной
            address = self.tasks[task_id].get('address', '')
            if address:
                print(f"Задача отмечена как невыполненная, добавляем маркер для адреса '{address}'")
                self.show_task_address_on_map(address, task_id)

        # Обновляем отображение
        self.update_tasks_display()

    def edit_task(self, event=None):
        """Открывает окно редактирования задачи"""
        # Получаем выбранную задачу
        selection = self.tasks_list.selection()
        if not selection:
            return

        task_id = int(selection[0])
        if task_id not in self.tasks:
            return

        task_info = self.tasks[task_id]

        # Создаем окно редактирования
        edit_window = ttk.Toplevel(self.root)
        edit_window.title("Редактирование задачи")
        edit_window.geometry("500x500")  # Увеличиваем высоту с 450 до 500
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Центрируем окно на экране
        edit_window.update_idletasks()
        self.center_window(edit_window)

        # Фрейм для полей редактирования
        edit_frame = ttk.Frame(edit_window, padding=10)
        edit_frame.pack(fill=tk.BOTH, expand=True)

        # Поле для даты (обычное поле ввода)
        ttk.Label(edit_frame, text="Дата:").grid(row=0, column=0, sticky=tk.W, pady=5)

        date_var = tk.StringVar(value=task_info['date'])
        date_entry = ttk.Entry(edit_frame, textvariable=date_var, width=15)
        date_entry.grid(row=0, column=1, sticky=tk.W, pady=5, padx=5)

        # Добавляем подсказку о формате даты
        ttk.Label(edit_frame, text="(дд.мм.гггг)", font=("Segoe UI", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)

        # Поле для задачи (многострочное)
        ttk.Label(edit_frame, text="Задача:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        task_text = tk.Text(edit_frame, height=3, width=40)
        task_text.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)
        task_text.insert('1.0', task_info['task'])

        # Галочка для отметки выполнения
        completed_var = tk.BooleanVar(value=task_info.get('completed', False))
        completed_check = ttk.Checkbutton(
            edit_frame,
            text="Выполнено",
            variable=completed_var
        )
        completed_check.grid(row=1, column=2, sticky=tk.W, pady=5, padx=5)

        # Поле для водителя
        ttk.Label(edit_frame, text="Водитель:").grid(row=2, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar(value=task_info['driver'])

        # Создаем комбобокс с водителями из реестра
        driver_combobox = ttk.Combobox(edit_frame, textvariable=driver_var, values=list(self.drivers_registry.keys()))
        driver_combobox.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для контрагента
        ttk.Label(edit_frame, text="Контрагент:").grid(row=3, column=0, sticky=tk.W, pady=5)
        contractor_var = tk.StringVar(value=task_info.get('contractor', ''))
        contractor_entry = ttk.Entry(edit_frame, textvariable=contractor_var)
        contractor_entry.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для адреса
        ttk.Label(edit_frame, text="Адрес:").grid(row=4, column=0, sticky=tk.W, pady=5)
        address_var = tk.StringVar(value=task_info.get('address', ''))
        address_entry = ttk.Entry(edit_frame, textvariable=address_var)
        address_entry.grid(row=4, column=1, sticky=tk.EW, pady=5, padx=5)

        # Кнопка для выбора адреса и контрагента из списка
        ttk.Button(
            edit_frame,
            text="Выбрать",
            width=10,
            command=lambda: self.select_address_for_task(address_var, contractor_var)
        ).grid(row=4, column=2, sticky=tk.W, pady=5, padx=5)

        # Поле для описания
        ttk.Label(edit_frame, text="Описание:").grid(row=5, column=0, sticky=tk.NW, pady=5)
        description_text = tk.Text(edit_frame, height=10, width=40)
        description_text.grid(row=5, column=1, columnspan=2, sticky=tk.NSEW, pady=5, padx=5)
        description_text.insert('1.0', task_info.get('description', ''))

        # Настраиваем веса строк и столбцов
        edit_frame.columnconfigure(1, weight=1)
        edit_frame.rowconfigure(3, weight=1)

        # Добавляем горячие клавиши
        edit_window.bind('<Control-v>', lambda e: self.paste_to_widget(e.widget))
        edit_window.bind('<Control-c>', lambda e: self.copy_from_widget(e.widget))

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.save_task_edit_with_address(
                edit_window, task_id, date_var.get(),
                task_text.get('1.0', 'end-1c'), driver_var.get(), description_text.get('1.0', 'end-1c'),
                completed_var.get(), contractor_var.get(), address_var.get()
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=edit_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def select_address_for_task(self, address_var, contractor_var=None):
        """Открывает окно выбора адреса и контрагента для задачи"""
        # Создаем окно выбора адреса
        select_window = ttk.Toplevel(self.root)
        select_window.title("Выбор адреса и контрагента")
        select_window.geometry("700x500")
        select_window.transient(self.root)
        select_window.grab_set()

        # Центрируем окно на экране
        select_window.update_idletasks()
        self.center_window(select_window)

        # Фрейм для списка адресов
        address_frame = ttk.Frame(select_window, padding=10)
        address_frame.pack(fill=tk.BOTH, expand=True)

        # Поле фильтрации
        filter_frame = ttk.Frame(address_frame)
        filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(filter_frame, text="Фильтр:").pack(side=tk.LEFT, padx=5)
        filter_var = tk.StringVar()
        filter_entry = ttk.Entry(filter_frame, textvariable=filter_var)
        filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Создаем Treeview для отображения адресов и контрагентов
        columns = ('contractor', 'address')
        address_tree = ttk.Treeview(address_frame, columns=columns, show='headings')
        address_tree.heading('contractor', text='Контрагент')
        address_tree.heading('address', text='Адрес')
        address_tree.column('contractor', width=200)
        address_tree.column('address', width=400)
        address_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Добавляем полосу прокрутки
        scrollbar = ttk.Scrollbar(address_tree, orient="vertical", command=address_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        address_tree.configure(yscrollcommand=scrollbar.set)

        # Заполняем список адресами и контрагентами из реестра
        for item in self.task_addresses_registry:
            address_tree.insert('', 'end', values=(item['contractor'], item['address']))

        # Функция фильтрации
        def apply_filter(*args):
            filter_text = filter_var.get().lower()
            # Очищаем список
            for item in address_tree.get_children():
                address_tree.delete(item)
            # Заполняем с учетом фильтра
            for item in self.task_addresses_registry:
                if (not filter_text or
                    filter_text in item['address'].lower() or
                    filter_text in item['contractor'].lower()):
                    address_tree.insert('', 'end', values=(item['contractor'], item['address']))

        # Привязываем функцию фильтрации к изменению поля фильтра
        filter_var.trace("w", apply_filter)

        # Фрейм для ручного ввода
        manual_frame = ttk.LabelFrame(select_window, text="Добавить новый адрес", padding=10)
        manual_frame.pack(fill=tk.X, padx=10, pady=5)

        # Поле для контрагента
        ttk.Label(manual_frame, text="Контрагент:").grid(row=0, column=0, sticky=tk.W, pady=5)
        new_contractor_var = tk.StringVar()
        new_contractor_entry = ttk.Entry(manual_frame, textvariable=new_contractor_var, width=30)
        new_contractor_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для адреса
        ttk.Label(manual_frame, text="Адрес:").grid(row=1, column=0, sticky=tk.W, pady=5)
        new_address_var = tk.StringVar()
        new_address_entry = ttk.Entry(manual_frame, textvariable=new_address_var, width=50)
        new_address_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Кнопка добавления
        ttk.Button(
            manual_frame,
            text="Добавить",
            command=lambda: self.add_new_task_address(
                new_contractor_var.get(),
                new_address_var.get(),
                address_tree
            )
        ).grid(row=2, column=1, sticky=tk.E, pady=5, padx=5)

        # Настраиваем веса столбцов
        manual_frame.columnconfigure(1, weight=1)

        # Кнопка удаления
        delete_button = ttk.Button(
            address_frame,
            text="Удалить выбранный",
            command=lambda: self.delete_task_address(address_tree)
        )
        delete_button.pack(side=tk.BOTTOM, pady=5)

        # Кнопки
        button_frame = ttk.Frame(select_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Выбрать",
            command=lambda: self.set_task_address_and_contractor(
                select_window,
                address_tree,
                address_var,
                contractor_var
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=select_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def delete_task_address(self, tree_widget):
        """Удаляет выбранный адрес из реестра"""
        selection = tree_widget.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите адрес для удаления")
            return

        # Получаем выбранный элемент
        item = tree_widget.item(selection[0])
        contractor, address = item['values']

        # Подтверждение удаления
        if not messagebox.askyesno("Подтверждение", f"Удалить адрес '{address}' и контрагента '{contractor}' из реестра?"):
            return

        # Удаляем из реестра
        for i, item in enumerate(self.task_addresses_registry):
            if item['address'] == address and item['contractor'] == contractor:
                del self.task_addresses_registry[i]
                break

        # Удаляем из дерева
        tree_widget.delete(selection[0])

        # Выводим сообщение
        print(f"Удален адрес: {address}, контрагент: {contractor}")

    def add_new_task_address(self, contractor, address, tree_widget):
        """Добавляет новый адрес и контрагента в реестр"""
        if not address:
            messagebox.showwarning("Предупреждение", "Введите адрес")
            return

        # Проверяем, есть ли уже такой адрес и контрагент в реестре
        for item in self.task_addresses_registry:
            if item['address'] == address and item['contractor'] == contractor:
                messagebox.showwarning("Предупреждение", f"Адрес '{address}' с контрагентом '{contractor}' уже есть в реестре")
                return

        # Добавляем в реестр
        new_item = {'contractor': contractor, 'address': address}
        self.task_addresses_registry.append(new_item)

        # Добавляем в дерево
        tree_widget.insert('', 'end', values=(contractor, address))

        # Выводим сообщение
        print(f"Добавлен новый адрес: {address}, контрагент: {contractor}")

    def set_task_address_and_contractor(self, window, tree_widget, address_var, contractor_var=None):
        """Устанавливает выбранный адрес и контрагент в поля задачи"""
        selection = tree_widget.selection()
        if selection:
            item = tree_widget.item(selection[0])
            contractor, address = item['values']

            # Устанавливаем значения
            address_var.set(address)
            if contractor_var:
                contractor_var.set(contractor)

            window.destroy()

    def save_task_edit_with_address(self, window, task_id, date, task, driver, description, completed=False, contractor='', address=''):
        """Сохраняет изменения в задаче с указанными контрагентом и адресом"""
        if task_id not in self.tasks:
            window.destroy()
            return

        print(f"Сохранение задачи с контрагентом='{contractor}', адресом='{address}'")

        # Если адрес и контрагент указаны, добавляем их в реестр
        if address and (address, contractor) not in [(item['address'], item['contractor']) for item in self.task_addresses_registry]:
            self.task_addresses_registry.append({
                'address': address,
                'contractor': contractor
            })
            print(f"Добавлен новый адрес в реестр: {address}, контрагент: {contractor}")

        # Получаем предыдущее состояние задачи
        previous_completed = self.tasks[task_id].get('completed', False)
        previous_address = self.tasks[task_id].get('address', '')

        # Обновляем информацию о задаче
        self.tasks[task_id] = {
            'date': date,
            'task': task,
            'driver': driver,
            'description': description,
            'completed': completed,
            'address': address,
            'contractor': contractor
        }

        # Если задача отмечена как выполненная, удаляем маркер с карты
        if completed and address:
            print(f"Задача отмечена как выполненная, удаляем маркер для адреса '{address}'")
            self.remove_task_address_from_map(address)
        # Если задача была выполнена, но теперь не выполнена, и адрес указан, отображаем его на карте
        elif previous_completed and not completed and address:
            print(f"Задача отмечена как невыполненная, отображаем адрес '{address}' на карте")
            self.show_task_address_on_map(address, task_id)
        # Если адрес изменился и задача не выполнена, обновляем маркер
        elif address != previous_address and not completed and address:
            # Если был предыдущий адрес, удаляем его маркер
            if previous_address:
                print(f"Адрес изменен, удаляем маркер для предыдущего адреса '{previous_address}'")
                self.remove_task_address_from_map(previous_address)

            # Отображаем новый адрес на карте
            print(f"Отображение нового адреса '{address}' на карте")
            self.show_task_address_on_map(address, task_id)

        # Обновляем отображение списка с сортировкой
        self.update_tasks_display()

        # Закрываем окно редактирования
        window.destroy()

    def save_task_edit(self, window, task_id, date, task, driver, description, completed=False):
        """Сохраняет изменения в задаче"""
        if task_id not in self.tasks:
            window.destroy()
            return

        # Получаем контрагента и адрес из полей формы
        try:
            # Выводим информацию о всех виджетах для отладки
            print("Поиск полей контрагента и адреса в окне редактирования задачи")
            print(f"Дочерние виджеты окна: {len(window.winfo_children())}")

            # Создаем переменные для хранения значений
            contractor = ''
            address = ''

            # Ищем все Entry виджеты в окне
            entries = []

            def find_entries(widget, level=0):
                indent = "  " * level
                if isinstance(widget, ttk.Entry):
                    entries.append(widget)
                    info = widget.grid_info()
                    value = widget.get()
                    print(f"{indent}Entry найден: row={info.get('row')}, value='{value}'")

                for child in widget.winfo_children():
                    find_entries(child, level + 1)

            # Запускаем поиск
            find_entries(window)

            print(f"Найдено {len(entries)} полей ввода")

            # Теперь ищем поля контрагента и адреса по их расположению
            # Предполагаем, что поле контрагента находится перед полем адреса
            contractor_entry = None
            address_entry = None

            # Сначала пробуем найти по номерам строк
            for entry in entries:
                row = entry.grid_info().get('row')
                if row == 3:  # Поле контрагента
                    contractor_entry = entry
                elif row == 4:  # Поле адреса
                    address_entry = entry

            # Если не нашли, пробуем найти по порядку (контрагент, затем адрес)
            if not contractor_entry or not address_entry:
                if len(entries) >= 2:
                    # Предполагаем, что контрагент - это первое поле, а адрес - второе
                    contractor_entry = entries[0]
                    address_entry = entries[1]

            # Получаем значения
            if contractor_entry:
                contractor = contractor_entry.get()
                print(f"Получено значение контрагента: '{contractor}'")

            if address_entry:
                address = address_entry.get()
                print(f"Получено значение адреса: '{address}'")

            if not contractor_entry or not address_entry:
                print("ВНИМАНИЕ: Не удалось найти поля контрагента или адреса")

        except Exception as e:
            print(f"Ошибка при получении контрагента и адреса: {e}")
            contractor = ''
            address = ''

        print(f"Итоговые значения: контрагент='{contractor}', адрес='{address}'")

        # Вызываем метод сохранения с явно указанными контрагентом и адресом
        self.save_task_edit_with_address(window, task_id, date, task, driver, description, completed, contractor, address)

    def paste_to_widget(self, widget):
        """Вставляет текст из буфера обмена в активный виджет"""
        try:
            clipboard_text = self.root.clipboard_get()

            if isinstance(widget, tk.Text):
                widget.insert(tk.INSERT, clipboard_text)
            elif isinstance(widget, ttk.Entry):
                widget.insert(tk.INSERT, clipboard_text)
        except:
            pass

    def copy_from_widget(self, widget):
        """Копирует текст из активного виджета в буфер обмена"""
        try:
            if isinstance(widget, tk.Text):
                if widget.tag_ranges(tk.SEL):
                    selected_text = widget.get(tk.SEL_FIRST, tk.SEL_LAST)
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
            elif isinstance(widget, ttk.Entry):
                if widget.selection_present():
                    selected_text = widget.selection_get()
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
        except:
            pass

    def save_tasks_list(self):
        """Сохранение списка задач в Excel файл"""
        tasks_file = "tasks_list.xlsx"

        try:
            # Создаем DataFrame из списка задач
            data = []
            for task_id, info in self.tasks.items():
                data.append({
                    'ID': task_id,
                    'Дата': info['date'],
                    'Задача': info['task'],
                    'Водитель': info['driver'],
                    'Описание': info['description'],
                    'Выполнено': info.get('completed', False),
                    'Адрес': info.get('address', ''),
                    'Контрагент': info.get('contractor', '')
                })

            if not data:
                print("Список задач пуст, нечего сохранять")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(tasks_file, index=False)
            print(f"Сохранено {len(data)} задач в список")
        except Exception as e:
            print(f"Ошибка при сохранении списка задач: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить список задач:\n{str(e)}")

    def load_tasks_list(self):
        """Загрузка списка задач из Excel файла"""
        tasks_file = "tasks_list.xlsx"

        if not os.path.exists(tasks_file):
            print("Файл списка задач не найден, будет создан новый")
            return

        try:
            df = pd.read_excel(tasks_file)

            # Проверяем наличие необходимых столбцов
            if all(col in df.columns for col in ['ID', 'Дата', 'Задача', 'Водитель']):
                # Очищаем текущий список задач
                for item in self.tasks_list.get_children():
                    self.tasks_list.delete(item)

                self.tasks.clear()
                max_id = 0

                for _, row in df.iterrows():
                    task_id = int(row['ID'])
                    date = row['Дата']
                    task = row['Задача']
                    driver = row['Водитель'] if pd.notna(row['Водитель']) else ''
                    description = row['Описание'] if 'Описание' in df.columns and pd.notna(row['Описание']) else ''
                    completed = bool(row['Выполнено']) if 'Выполнено' in df.columns and pd.notna(row['Выполнено']) else False
                    address = row['Адрес'] if 'Адрес' in df.columns and pd.notna(row['Адрес']) else ''
                    contractor = row['Контрагент'] if 'Контрагент' in df.columns and pd.notna(row['Контрагент']) else ''

                    # Добавляем задачу в словарь
                    self.tasks[task_id] = {
                        'date': date,
                        'task': task,
                        'driver': driver,
                        'description': description,
                        'completed': completed,
                        'address': address,
                        'contractor': contractor
                    }

                    # Если адрес и контрагент указаны, добавляем их в реестр
                    if address and (address, contractor) not in [(item['address'], item['contractor']) for item in self.task_addresses_registry]:
                        self.task_addresses_registry.append({
                            'address': address,
                            'contractor': contractor
                        })

                    # Если у задачи есть адрес и она не отмечена как выполненная, отображаем его на карте
                    if address and not completed:
                        self.show_task_address_on_map(address, task_id)
                    elif address and completed:
                        print(f"Задача {task_id} отмечена как выполненная, адрес '{address}' не отображается на карте")

                    # Обновляем максимальный ID
                    if task_id > max_id:
                        max_id = task_id

                # Устанавливаем следующий ID
                self.next_task_id = max_id + 1

                # Обновляем заголовок фрейма
                self.tasks_frame.config(text=f"📋 Управление задачами ({len(self.tasks)})")

                # Обновляем отображение с сортировкой
                self.update_tasks_display()

                print(f"Загружено {len(self.tasks)} задач из списка")
            else:
                print("Некорректный формат файла списка задач")
        except Exception as e:
            print(f"Ошибка при загрузке списка задач: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить список задач:\n{str(e)}")

    def center_window(self, window):
        """Центрирует окно на экране"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry('{}x{}+{}+{}'.format(width, height, x, y))

    def show_modern_message(self, title, message, msg_type="info"):
        """Показывает современное сообщение в стиле программы"""
        # Создаем диалоговое окно
        dialog = ttk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("350x250")  # Сделали уже (350 вместо 400) и выше (250 вместо 200)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # Центрируем окно
        self.center_window(dialog)

        # Основной фрейм с отступами
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Иконка в зависимости от типа сообщения
        icon_text = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌"
        }.get(msg_type, "ℹ️")

        # Фрейм для иконки и заголовка
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        # Иконка
        icon_label = ttk.Label(header_frame, text=icon_text, font=("Segoe UI", 16))
        icon_label.pack(side=tk.LEFT, padx=(0, 10))

        # Заголовок
        title_label = ttk.Label(header_frame, text=title, font=("Segoe UI", 12, "bold"))
        title_label.pack(side=tk.LEFT)

        # Текст сообщения
        message_label = ttk.Label(main_frame, text=message, font=("Segoe UI", 10),
                                 wraplength=350, justify=tk.LEFT)
        message_label.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Кнопка OK
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Определяем стиль кнопки в зависимости от типа сообщения
        button_style = {
            "info": "info.TButton",
            "success": "success.TButton",
            "warning": "warning.TButton",
            "error": "danger.TButton"
        }.get(msg_type, "info.TButton")

        ok_button = ttk.Button(button_frame, text="OK", style=button_style,
                              command=dialog.destroy)
        ok_button.pack(side=tk.RIGHT)

        # Фокус на кнопку и привязка Enter
        ok_button.focus_set()
        dialog.bind('<Return>', lambda e: dialog.destroy())
        dialog.bind('<Escape>', lambda e: dialog.destroy())

        # Ждем закрытия окна
        self.root.wait_window(dialog)

    def remove_task_address_from_map(self, address):
        """Удаляет маркер адреса задачи с карты"""
        if not address:
            print("Пустой адрес, нечего удалять с карты")
            return

        print(f"Удаление маркера для адреса задачи: {address}")

        # Проверяем, есть ли адрес в списке маркеров
        if address in self.markers:
            try:
                # Получаем маркер
                marker = self.markers[address]

                # Удаляем маркер с карты
                marker.delete()

                # Удаляем из хранилищ
                del self.markers[address]
                if address in self.original_markers:
                    del self.original_markers[address]

                # Удаляем из словаря task_addresses
                if hasattr(self, 'task_addresses') and address in self.task_addresses:
                    del self.task_addresses[address]

                print(f"Маркер для адреса '{address}' успешно удален с карты")
            except Exception as e:
                print(f"Ошибка при удалении маркера для адреса '{address}': {e}")
        else:
            print(f"Маркер для адреса '{address}' не найден на карте")

    def show_task_address_on_map(self, address, task_id):
        """Отображает адрес задачи на карте фиолетовым цветом"""
        if not address:
            print("Пустой адрес, нечего отображать на карте")
            return

        print(f"Отображение адреса задачи на карте: {address}")

        # Проверяем, есть ли адрес в списке маркеров
        if address not in self.original_markers and address not in self.markers:
            # Если адреса нет на карте, пробуем геокодировать его
            # Используем флаг is_task_address=True, чтобы не добавлять в кэш
            lat, lng = self.geocode_address(address, is_task_address=True)
            if lat is None or lng is None:
                print(f"Не удалось найти координаты для адреса: {address}")
                messagebox.showwarning("Предупреждение", f"Не удалось найти координаты для адреса: {address}")
                return

            print(f"Получены координаты для адреса: {address} - {lat}, {lng}")

            # Создаем новый маркер
            try:
                marker = self.map_widget.set_marker(
                    lat, lng,
                    text=address,
                    marker_color_circle="purple",
                    marker_color_outside="purple"
                )

                # Добавляем маркер в хранилища
                self.markers[address] = marker
                self.original_markers[address] = marker

                # Добавляем адрес в словарь значений с нулевыми значениями суммы и веса
                # но только для внутреннего использования, не добавляем в основной список
                self.address_values[address] = {'sum': 0.0, 'weight': 0.0}

                # Адреса задач не добавляем в основной список
                print(f"Адрес задачи '{address}' не добавляется в основной список")

                # Центрируем карту на новом маркере
                self.map_widget.set_position(lat, lng)

                print(f"Добавлен новый адрес из задачи: {address}")
            except Exception as e:
                print(f"Ошибка при создании маркера: {e}")
                messagebox.showerror("Ошибка", f"Не удалось создать маркер для адреса: {address}\n{str(e)}")
                return
        else:
            # Если адрес уже есть на карте, меняем его цвет на фиолетовый
            if address in self.markers:
                try:
                    # Получаем текущий маркер
                    marker = self.markers[address]
                    lat, lng = marker.position

                    # Удаляем текущий маркер
                    marker.delete()

                    # Создаем новый маркер фиолетового цвета
                    new_marker = self.map_widget.set_marker(
                        lat, lng,
                        text=address,
                        marker_color_circle="purple",
                        marker_color_outside="purple"
                    )

                    # Обновляем хранилища
                    self.markers[address] = new_marker

                    # Центрируем карту на маркере
                    self.map_widget.set_position(lat, lng)

                    print(f"Изменен цвет маркера для адреса задачи: {address}")
                except Exception as e:
                    print(f"Ошибка при изменении цвета маркера: {e}")
                    messagebox.showerror("Ошибка", f"Не удалось изменить цвет маркера для адреса: {address}\n{str(e)}")
                    return

        # Сохраняем информацию о том, что этот адрес связан с задачей
        if not hasattr(self, 'task_addresses'):
            self.task_addresses = {}
        self.task_addresses[address] = task_id

    def save_task_addresses_registry(self):
        """Сохраняет реестр адресов и контрагентов из задач"""
        if not self.task_addresses_registry:
            print("Реестр адресов и контрагентов пуст, нечего сохранять")
            return

        try:
            # Создаем DataFrame из реестра
            df = pd.DataFrame(self.task_addresses_registry)

            # Сохраняем в Excel
            with pd.ExcelWriter("task_addresses_registry.xlsx", engine='openpyxl') as writer:
                df.to_excel(writer, index=False)

            print(f"Реестр адресов и контрагентов сохранен ({len(self.task_addresses_registry)} записей)")
        except Exception as e:
            print(f"Ошибка при сохранении реестра адресов и контрагентов: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить реестр адресов и контрагентов:\n{str(e)}")

    def load_task_addresses_registry(self):
        """Загружает реестр адресов и контрагентов из задач"""
        registry_file = "task_addresses_registry.xlsx"

        if not os.path.exists(registry_file):
            print("Файл реестра адресов и контрагентов не найден")
            return

        try:
            # Загружаем данные из Excel
            df = pd.read_excel(registry_file)

            # Проверяем наличие необходимых столбцов
            if 'address' in df.columns and 'contractor' in df.columns:
                # Очищаем текущий реестр
                self.task_addresses_registry = []

                # Заполняем реестр данными из файла
                for _, row in df.iterrows():
                    address = row['address'] if pd.notna(row['address']) else ''
                    contractor = row['contractor'] if pd.notna(row['contractor']) else ''

                    if address:
                        self.task_addresses_registry.append({
                            'address': address,
                            'contractor': contractor
                        })

                print(f"Загружено {len(self.task_addresses_registry)} адресов и контрагентов из реестра")
            else:
                print("Некорректный формат файла реестра адресов и контрагентов")
        except Exception as e:
            print(f"Ошибка при загрузке реестра адресов и контрагентов: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить реестр адресов и контрагентов:\n{str(e)}")

    def update_schedule_display(self, event=None):
        """Обновляет отображение расписания для выбранного дня недели"""
        # Очищаем текущее содержимое таблицы
        for item in self.schedule_tree.get_children():
            self.schedule_tree.delete(item)

        # Получаем выбранный день недели
        day = self.day_var.get()

        # Заполняем таблицу данными для выбранного дня
        if day in self.drivers_schedule:
            for entry in self.drivers_schedule[day]:
                driver_name = entry['driver']
                region = entry['region']

                # Получаем информацию о цвете, количестве точек и сумме
                color = "Н/Д"
                points_count = 0
                total_sum = 0.0

                # Проверяем, инициализированы ли сохраненные водители
                if hasattr(self, 'saved_drivers') and self.saved_drivers:
                    # Ищем данные для этого водителя в этом регионе
                    driver_data = None

                    # Сначала пытаемся найти точное совпадение по имени и региону
                    for key, driver_info in self.saved_drivers.items():
                        if (driver_info.get('driver_name', key) == driver_name and
                            driver_info.get('region') == region):
                            driver_data = driver_info
                            break

                    # Если точное совпадение не найдено, ищем по имени водителя
                    if not driver_data and driver_name:
                        for key, driver_info in self.saved_drivers.items():
                            if driver_info.get('driver_name', key) == driver_name:
                                driver_data = driver_info
                                break

                    # Если и по имени не найдено, ищем по ключу (для обратной совместимости)
                    if not driver_data and driver_name:
                        if driver_name in self.saved_drivers:
                            driver_data = self.saved_drivers[driver_name]

                    if driver_data:
                        # Получаем цвет маркера
                        if 'color' in driver_data:
                            color = driver_data['color']

                        # Получаем количество точек
                        if 'count' in driver_data:
                            points_count = driver_data['count']
                        elif 'addresses' in driver_data:
                            points_count = len(driver_data['addresses'])

                        # Получаем сумму
                        if 'sum' in driver_data:
                            total_sum = driver_data['sum']
                        else:
                            # Если сумма не сохранена, вычисляем ее из адресов
                            addresses = driver_data.get('addresses', [])
                            total_sum = sum(self.address_values.get(addr, {}).get('sum', 0.0) for addr in addresses)

                # Вставляем запись в таблицу
                item_id = self.schedule_tree.insert('', 'end', values=(
                    region,
                    driver_name,
                    color,
                    points_count,
                    f"{total_sum:.2f}"
                ))

                # Если есть цвет, настраиваем тег для отображения цвета
                if color != "Н/Д" and color:
                    try:
                        # Создаем тег для этого цвета, если его еще нет
                        tag_name = f"color_{color.replace('#', '')}"

                        # Проверяем, существует ли тег
                        tag_exists = False
                        try:
                            # Пытаемся получить конфигурацию тега
                            tag_config = self.schedule_tree.tag_configure(tag_name)
                            tag_exists = True
                        except:
                            tag_exists = False

                        # Если тег не существует, создаем его
                        if not tag_exists:
                            self.schedule_tree.tag_configure(tag_name, background=color)

                        # Применяем тег к ячейке с цветом
                        self.schedule_tree.item(item_id, tags=(tag_name,))
                    except Exception as e:
                        print(f"Ошибка при настройке тега цвета: {e}")

    def edit_schedule_entry(self, event=None):
        """Редактирование записи в расписании"""
        # Получаем выбранную запись
        selection = self.schedule_tree.selection()
        if not selection:
            return

        # Получаем данные выбранной записи
        item = self.schedule_tree.item(selection[0])
        values = item['values']

        # Проверяем, что у нас есть как минимум 2 значения (регион и водитель)
        if len(values) < 2:
            messagebox.showwarning("Предупреждение", "Некорректный формат записи")
            return

        region, driver = values[0], values[1]

        # Создаем диалоговое окно для редактирования
        edit_window = ttk.Toplevel(self.root)
        edit_window.title("Редактирование записи")
        edit_window.geometry("500x150")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Центрируем окно на экране
        self.center_window(edit_window)

        # Создаем фрейм для полей ввода
        edit_frame = ttk.Frame(edit_window, padding=10)
        edit_frame.pack(fill=tk.BOTH, expand=True)

        # Поле для региона
        ttk.Label(edit_frame, text="Регион:").grid(row=0, column=0, sticky=tk.W, pady=5)
        region_var = tk.StringVar(value=region)
        region_entry = ttk.Entry(edit_frame, textvariable=region_var, width=40)
        region_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для водителя
        ttk.Label(edit_frame, text="Водитель:").grid(row=1, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar(value=driver)

        # Создаем комбобокс с водителями из реестра
        driver_values = list(self.drivers_registry.keys())
        driver_combobox = ttk.Combobox(edit_frame, textvariable=driver_var, values=driver_values)
        driver_combobox.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Настраиваем веса столбцов
        edit_frame.columnconfigure(1, weight=1)

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.save_schedule_entry(
                edit_window, selection[0], region, region_var.get(), driver_var.get(), driver
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=edit_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def save_schedule_entry(self, window, item_id, old_region, new_region, new_driver, old_driver=None):
        """Сохраняет изменения в записи расписания"""
        day = self.day_var.get()

        # Если старый водитель не передан, пытаемся получить его из текущих данных
        if old_driver is None:
            current_values = self.schedule_tree.item(item_id, 'values')
            if len(current_values) >= 2:
                old_driver = current_values[1]

        # Получаем индекс записи в списке
        index = None
        old_entry = None
        for i, entry in enumerate(self.drivers_schedule[day]):
            if entry['region'] == old_region and entry['driver'] == old_driver:
                index = i
                old_entry = entry.copy()  # Сохраняем копию старой записи
                break

        if index is not None:
            # Сохраняем существующие данные о количестве точек и сумме
            updated_entry = {'region': new_region, 'driver': new_driver}

            # Переносим дополнительные данные из старой записи, если они есть
            if old_entry:
                for key in ['color', 'count', 'sum']:
                    if key in old_entry:
                        updated_entry[key] = old_entry[key]

            # Обновляем запись, сохраняя существующие данные
            self.drivers_schedule[day][index] = updated_entry

            # Также обновляем данные в saved_drivers, если водитель изменился
            if old_driver and old_driver != new_driver and hasattr(self, 'saved_drivers'):
                # Ищем данные старого водителя в этом регионе
                old_driver_key = None
                for key, driver_info in self.saved_drivers.items():
                    if (driver_info.get('driver_name', key) == old_driver and
                        driver_info.get('region') == old_region):
                        old_driver_key = key
                        break

                if old_driver_key:
                    # Копируем данные старого водителя для нового водителя
                    old_driver_data = self.saved_drivers[old_driver_key].copy()

                    # Создаем новый ключ для нового водителя
                    new_driver_key = f"{new_driver}_{new_region}_{day}_{index}"

                    # Обновляем имя водителя в данных
                    old_driver_data['driver_name'] = new_driver
                    old_driver_data['region'] = new_region

                    # Сохраняем данные под новым ключом
                    self.saved_drivers[new_driver_key] = old_driver_data

                    # Удаляем старую запись
                    del self.saved_drivers[old_driver_key]

                    print(f"Переназначен водитель с '{old_driver}' на '{new_driver}' в регионе '{new_region}', данные сохранены")
        else:
            # Если запись не найдена, добавляем новую
            self.drivers_schedule[day].append({'region': new_region, 'driver': new_driver})

        # Обновляем отображение в таблице
        self.update_schedule_display()

        # Закрываем окно редактирования
        window.destroy()

        # Автоматически сохраняем расписание в файл
        self.save_schedule(show_message=True)

        # Сохраняем обновленные данные водителей
        self.save_drivers_addresses()

    def edit_schedule(self):
        """Открывает окно для редактирования всего расписания на выбранный день"""
        day = self.day_var.get()

        # Создаем диалоговое окно для редактирования
        edit_window = ttk.Toplevel(self.root)
        edit_window.title(f"Редактирование расписания на {day}")
        edit_window.geometry("600x400")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Центрируем окно на экране
        self.center_window(edit_window)

        # Создаем фрейм для таблицы
        table_frame = ttk.Frame(edit_window, padding=10)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # Создаем таблицу для редактирования
        columns = ("region", "driver")
        edit_tree = ttk.Treeview(table_frame, columns=columns, show="headings")
        edit_tree.heading("region", text="Регион")
        edit_tree.heading("driver", text="Водитель")
        edit_tree.column("region", width=300)
        edit_tree.column("driver", width=150)

        # Добавляем полосу прокрутки
        edit_scroll = ttk.Scrollbar(table_frame, orient="vertical", command=edit_tree.yview)
        edit_tree.configure(yscrollcommand=edit_scroll.set)

        # Размещаем таблицу и полосу прокрутки
        edit_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        edit_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Заполняем таблицу данными
        for entry in self.drivers_schedule[day]:
            edit_tree.insert('', 'end', values=(entry['region'], entry['driver']))

        # Фрейм для кнопок управления
        control_frame = ttk.Frame(edit_window, padding=10)
        control_frame.pack(fill=tk.X)

        # Кнопки для добавления, редактирования и удаления записей
        ttk.Button(
            control_frame,
            text="Добавить запись",
            command=lambda: self.add_schedule_entry(edit_tree, day)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            control_frame,
            text="Редактировать запись",
            command=lambda: self.edit_schedule_entry_in_dialog(edit_tree, day)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            control_frame,
            text="Удалить запись",
            command=lambda: self.delete_schedule_entry(edit_tree, day)
        ).pack(side=tk.LEFT, padx=5)

        # Привязываем двойной клик для редактирования записи
        edit_tree.bind("<Double-1>", lambda e: self.edit_schedule_entry_in_dialog(edit_tree, day))

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.save_schedule_changes(edit_window, edit_tree, day)
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=edit_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def add_schedule_entry(self, tree, day):
        """Добавляет новую запись в расписание"""
        # Создаем диалоговое окно для добавления
        add_window = ttk.Toplevel(self.root)
        add_window.title("Добавление записи")
        add_window.geometry("500x150")
        add_window.transient(self.root)
        add_window.grab_set()

        # Центрируем окно на экране
        self.center_window(add_window)

        # Создаем фрейм для полей ввода
        add_frame = ttk.Frame(add_window, padding=10)
        add_frame.pack(fill=tk.BOTH, expand=True)

        # Поле для региона
        ttk.Label(add_frame, text="Регион:").grid(row=0, column=0, sticky=tk.W, pady=5)
        region_var = tk.StringVar()
        region_entry = ttk.Entry(add_frame, textvariable=region_var, width=40)
        region_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для водителя
        ttk.Label(add_frame, text="Водитель:").grid(row=1, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar()

        # Создаем комбобокс с водителями из реестра
        driver_values = list(self.drivers_registry.keys())
        driver_combobox = ttk.Combobox(add_frame, textvariable=driver_var, values=driver_values)
        driver_combobox.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Настраиваем веса столбцов
        add_frame.columnconfigure(1, weight=1)

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(add_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Добавить",
            command=lambda: self.confirm_add_schedule_entry(
                add_window, tree, day, region_var.get(), driver_var.get()
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=add_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def confirm_add_schedule_entry(self, window, tree, day, region, driver):
        """Подтверждает добавление новой записи в расписание"""
        if not region:
            messagebox.showwarning("Предупреждение", "Введите регион")
            return

        # Проверяем, есть ли уже такой регион в расписании
        existing_regions = [entry['region'] for entry in self.drivers_schedule[day]]
        if region in existing_regions:
            # Считаем количество одинаковых регионов
            count = existing_regions.count(region) + 1
            suggested_region = f"{region} {count}"

            # Предлагаем пользователю использовать нумерацию
            if messagebox.askyesno("Дублирующийся регион",
                                 f"Регион '{region}' уже существует в расписании.\n"
                                 f"Хотите использовать название '{suggested_region}'?"):
                region = suggested_region

        # Добавляем запись в таблицу
        tree.insert('', 'end', values=(region, driver))

        # Добавляем запись в расписание
        self.drivers_schedule[day].append({'region': region, 'driver': driver})

        # Закрываем окно добавления
        window.destroy()

        # Автоматически сохраняем расписание в файл
        self.save_schedule(show_message=True)

    def edit_schedule_entry_in_dialog(self, tree, day, event=None):
        """Редактирует запись в окне редактирования расписания"""
        # Получаем выбранную запись
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите запись для редактирования")
            return

        # Получаем данные выбранной записи
        item = tree.item(selection[0])
        region, driver = item['values']

        # Создаем диалоговое окно для редактирования
        edit_window = ttk.Toplevel(self.root)
        edit_window.title("Редактирование записи")
        edit_window.geometry("500x150")
        edit_window.transient(self.root)
        edit_window.grab_set()

        # Центрируем окно на экране
        self.center_window(edit_window)

        # Создаем фрейм для полей ввода
        edit_frame = ttk.Frame(edit_window, padding=10)
        edit_frame.pack(fill=tk.BOTH, expand=True)

        # Поле для региона
        ttk.Label(edit_frame, text="Регион:").grid(row=0, column=0, sticky=tk.W, pady=5)
        region_var = tk.StringVar(value=region)
        region_entry = ttk.Entry(edit_frame, textvariable=region_var, width=40)
        region_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для водителя
        ttk.Label(edit_frame, text="Водитель:").grid(row=1, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar(value=driver)

        # Создаем комбобокс с водителями из реестра
        driver_values = list(self.drivers_registry.keys())
        driver_combobox = ttk.Combobox(edit_frame, textvariable=driver_var, values=driver_values)
        driver_combobox.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Настраиваем веса столбцов
        edit_frame.columnconfigure(1, weight=1)

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.confirm_edit_schedule_entry(
                edit_window, tree, selection[0], region_var.get(), driver_var.get()
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=edit_window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def confirm_edit_schedule_entry(self, window, tree, item_id, new_region, new_driver):
        """Подтверждает редактирование записи в расписании"""
        if not new_region:
            self.show_modern_message("Введите регион", "Введите регион", "warning")
            return

        # Получаем старые значения перед обновлением
        old_values = tree.item(item_id, 'values')
        old_region = old_values[0]
        old_driver = old_values[1]

        # Обновляем запись в таблице
        tree.item(item_id, values=(new_region, new_driver))

        # Получаем текущий день
        day = self.day_var.get()

        # Ищем запись в расписании и сохраняем существующие данные
        for i, entry in enumerate(self.drivers_schedule[day]):
            if entry['region'] == old_region and entry['driver'] == old_driver:
                # Сохраняем существующие данные о количестве точек и сумме
                updated_entry = {'region': new_region, 'driver': new_driver}

                # Переносим дополнительные данные из старой записи, если они есть
                for key in ['color', 'count', 'sum']:
                    if key in entry:
                        updated_entry[key] = entry[key]

                # Обновляем запись, сохраняя существующие данные
                self.drivers_schedule[day][i] = updated_entry

                # Также обновляем данные в saved_drivers, если водитель изменился
                if old_driver != new_driver and hasattr(self, 'saved_drivers'):
                    # Ищем данные старого водителя в этом регионе
                    old_driver_key = None
                    for key, driver_info in self.saved_drivers.items():
                        if (driver_info.get('driver_name', key) == old_driver and
                            driver_info.get('region') == old_region):
                            old_driver_key = key
                            break

                    if old_driver_key:
                        # Копируем данные старого водителя для нового водителя
                        old_driver_data = self.saved_drivers[old_driver_key].copy()

                        # Создаем новый ключ для нового водителя
                        new_driver_key = f"{new_driver}_{new_region}_{day}_{i}"

                        # Обновляем имя водителя в данных
                        old_driver_data['driver_name'] = new_driver
                        old_driver_data['region'] = new_region

                        # Сохраняем данные под новым ключом
                        self.saved_drivers[new_driver_key] = old_driver_data

                        # Удаляем старую запись
                        del self.saved_drivers[old_driver_key]

                        print(f"Переназначен водитель с '{old_driver}' на '{new_driver}' в регионе '{new_region}', данные сохранены")
                break
        else:
            # Если запись не найдена, добавляем новую
            self.drivers_schedule[day].append({'region': new_region, 'driver': new_driver})

        # Закрываем окно редактирования
        window.destroy()

        # Обновляем отображение в основной таблице
        self.update_schedule_display()

        # Автоматически сохраняем расписание в файл
        self.save_schedule(show_message=True)

        # Сохраняем обновленные данные водителей
        self.save_drivers_addresses()

    def delete_schedule_entry(self, tree, day):
        """Удаляет запись из расписания"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите запись для удаления")
            return

        # Удаляем выбранную запись из таблицы
        tree.delete(selection[0])

        # Автоматически сохраняем изменения
        self.save_schedule_changes(None, tree, day)

    def save_schedule_changes(self, window, tree, day):
        """Сохраняет изменения в расписании"""
        # Очищаем текущее расписание для выбранного дня
        self.drivers_schedule[day] = []

        # Собираем данные из таблицы
        for item_id in tree.get_children():
            item = tree.item(item_id)
            region, driver = item['values']
            self.drivers_schedule[day].append({'region': region, 'driver': driver})

        # Обновляем отображение в основной таблице
        self.update_schedule_display()

        # Закрываем окно редактирования, если оно есть
        if window:
            window.destroy()

        # Сохраняем расписание в файл
        self.save_schedule(show_message=True)

    def save_schedule(self, show_message=False):
        """Сохраняет расписание в Excel файл"""
        schedule_file = "drivers_schedule.xlsx"

        try:
            # Создаем DataFrame для каждого дня недели
            dfs = {}
            for day in self.drivers_schedule:
                data = []
                for entry in self.drivers_schedule[day]:
                    driver_name = entry['driver']
                    region = entry['region']

                    # Получаем информацию о цвете, количестве точек и сумме
                    color = "Н/Д"
                    points_count = 0
                    total_sum = 0.0

                    # Проверяем, инициализированы ли сохраненные водители
                    if hasattr(self, 'saved_drivers'):
                        # Ищем данные для этого водителя в этом регионе
                        driver_data = None
                        for key, driver_info in self.saved_drivers.items():
                            # Проверяем по имени водителя и региону
                            if (driver_info.get('driver_name', key) == driver_name and
                                driver_info.get('region') == region):
                                driver_data = driver_info
                                break

                        if driver_data:
                            # Получаем цвет маркера
                            if 'color' in driver_data:
                                color = driver_data['color']

                            # Получаем количество точек
                            if 'count' in driver_data:
                                points_count = driver_data['count']
                            elif 'addresses' in driver_data:
                                points_count = len(driver_data['addresses'])

                            # Получаем сумму
                            if 'sum' in driver_data:
                                total_sum = driver_data['sum']
                            else:
                                # Если сумма не сохранена, вычисляем ее из адресов
                                addresses = driver_data.get('addresses', [])
                                total_sum = sum(self.address_values.get(addr, {}).get('sum', 0.0) for addr in addresses)

                    data.append({
                        'Регион': region,
                        'Водитель': driver_name,
                        'Цвет': color,
                        'Количество': points_count,
                        'Сумма': total_sum
                    })

                if data:
                    dfs[day] = pd.DataFrame(data)
                else:
                    dfs[day] = pd.DataFrame(columns=['Регион', 'Водитель', 'Цвет', 'Количество', 'Сумма'])

            # Сохраняем в Excel (каждый день на отдельном листе)
            with pd.ExcelWriter(schedule_file, engine='openpyxl') as writer:
                for day, df in dfs.items():
                    df.to_excel(writer, sheet_name=day, index=False)

            print(f"Расписание сохранено в файл {schedule_file}")
            if show_message:
                messagebox.showinfo("Сохранение", "Расписание успешно сохранено")
        except Exception as e:
            print(f"Ошибка при сохранении расписания: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить расписание:\n{str(e)}")

    def load_schedule(self):
        """Загружает расписание из Excel файла"""
        schedule_file = "drivers_schedule.xlsx"

        if not os.path.exists(schedule_file):
            print("Файл расписания не найден, будет использовано стандартное расписание")
            return

        try:
            # Загружаем данные из Excel
            xls = pd.ExcelFile(schedule_file)

            # Очищаем текущее расписание
            self.drivers_schedule = {
                'Понедельник': [],
                'Вторник': [],
                'Среда': [],
                'Четверг': [],
                'Пятница': []
            }

            # Загружаем данные для каждого дня недели
            for day in self.drivers_schedule:
                if day in xls.sheet_names:
                    df = pd.read_excel(schedule_file, sheet_name=day)

                    # Проверяем наличие необходимых столбцов
                    if 'Регион' in df.columns and 'Водитель' in df.columns:
                        for _, row in df.iterrows():
                            region = row['Регион'] if pd.notna(row['Регион']) else ''
                            driver = row['Водитель'] if pd.notna(row['Водитель']) else ''

                            if region:
                                entry = {'region': region, 'driver': driver}

                                # Проверяем, есть ли дополнительные столбцы
                                if 'Цвет' in df.columns:
                                    color = row['Цвет'] if pd.notna(row['Цвет']) else 'Н/Д'
                                    entry['color'] = color

                                if 'Количество' in df.columns:
                                    count = row['Количество'] if pd.notna(row['Количество']) else 0
                                    entry['count'] = count

                                if 'Сумма' in df.columns:
                                    total_sum = row['Сумма'] if pd.notna(row['Сумма']) else 0.0
                                    entry['sum'] = total_sum

                                self.drivers_schedule[day].append(entry)

                                # Обновляем информацию в saved_drivers, если водитель существует
                                if hasattr(self, 'saved_drivers') and driver in self.saved_drivers:
                                    if 'Цвет' in df.columns and color != 'Н/Д':
                                        self.saved_drivers[driver]['color'] = color

                                    if 'Количество' in df.columns and count > 0:
                                        self.saved_drivers[driver]['count'] = count

                                    if 'Сумма' in df.columns and total_sum > 0:
                                        self.saved_drivers[driver]['sum'] = total_sum
                                elif hasattr(self, 'saved_drivers'):
                                    # Если водителя нет в saved_drivers, добавляем его
                                    self.saved_drivers[driver] = {}

                                    if 'Цвет' in df.columns and color != 'Н/Д':
                                        self.saved_drivers[driver]['color'] = color

                                    if 'Количество' in df.columns and count > 0:
                                        self.saved_drivers[driver]['count'] = count

                                    if 'Сумма' in df.columns and total_sum > 0:
                                        self.saved_drivers[driver]['sum'] = total_sum

            # Обновляем отображение
            self.update_schedule_display()

            print(f"Расписание загружено из файла {schedule_file}")
        except Exception as e:
            print(f"Ошибка при загрузке расписания: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить расписание:\n{str(e)}")

    def show_schedule_context_menu(self, event):
        """Отображает контекстное меню для таблицы расписания"""
        # Получаем элемент, на котором было вызвано контекстное меню
        item = self.schedule_tree.identify_row(event.y)
        if item:
            # Выделяем элемент
            self.schedule_tree.selection_set(item)
            # Отображаем контекстное меню
            self.schedule_menu.post(event.x_root, event.y_root)

    def get_selected_driver_from_schedule(self):
        """Получает имя водителя из выбранной записи в расписании"""
        selection = self.schedule_tree.selection()
        if not selection:
            self.show_modern_message("Выберите запись", "Выберите запись в расписании", "warning")
            return None

        # Получаем данные выбранной записи
        item = self.schedule_tree.item(selection[0])
        driver_name = item['values'][1]  # Имя водителя во втором столбце

        return driver_name

    def show_driver_addresses_on_map(self):
        """Отображает адреса выбранного водителя на карте"""
        driver_name = self.get_selected_driver_from_schedule()
        if not driver_name:
            return

        # Проверяем, инициализирован ли словарь сохраненных водителей
        if not hasattr(self, 'saved_drivers'):
            messagebox.showinfo("Информация", "Список сохраненных водителей еще не инициализирован")
            return

        # Получаем регион из выбранной записи в расписании
        selection = self.schedule_tree.selection()
        if not selection:
            self.show_modern_message("Выберите запись", "Выберите запись в расписании", "warning")
            return

        item = self.schedule_tree.item(selection[0])
        region = item['values'][0]  # Регион в первом столбце

        # Ищем данные для этого водителя в этом регионе (гибкий поиск)
        driver_data = None

        # Сначала пытаемся найти точное совпадение по имени и региону
        for key, driver_info in self.saved_drivers.items():
            if (driver_info.get('driver_name', key) == driver_name and
                driver_info.get('region') == region):
                driver_data = driver_info
                break

        # Если точное совпадение не найдено, ищем по имени водителя
        if not driver_data and driver_name:
            for key, driver_info in self.saved_drivers.items():
                if driver_info.get('driver_name', key) == driver_name:
                    driver_data = driver_info
                    break

        # Если и по имени не найдено, ищем по ключу (для обратной совместимости)
        if not driver_data and driver_name:
            if driver_name in self.saved_drivers:
                driver_data = self.saved_drivers[driver_name]

        if not driver_data:
            self.show_modern_message("Нет данных", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Получаем адреса и цвет водителя
        addresses = driver_data.get('addresses', [])
        color = driver_data.get('color', 'red')  # Используем красный цвет по умолчанию

        # Получаем информацию о сумме
        total_sum = driver_data.get('sum', 0.0)

        if not addresses:
            self.show_modern_message("Нет адресов", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Отображаем адреса на карте
        for address in addresses:
            # Проверяем, есть ли адрес уже на карте
            if address in self.markers:
                continue

            # Геокодируем адрес
            lat, lng = self.geocode_address(address)
            if lat is None or lng is None:
                print(f"Не удалось найти координаты для адреса: {address}")
                continue

            # Создаем маркер
            marker = self.map_widget.set_marker(
                lat, lng,
                text=address,
                marker_color_circle=color,
                marker_color_outside=color
            )

            # Добавляем маркер в словари
            self.markers[address] = marker
            self.original_markers[address] = marker

        self.show_modern_message("Адреса отображены", f"Отображено {len(addresses)} адресов водителя {driver_name}", "info")

    def hide_driver_addresses_from_map(self):
        """Удаляет адреса выбранного водителя с карты"""
        driver_name = self.get_selected_driver_from_schedule()
        if not driver_name:
            return

        # Проверяем, инициализирован ли словарь сохраненных водителей
        if not hasattr(self, 'saved_drivers'):
            messagebox.showinfo("Информация", "Список сохраненных водителей еще не инициализирован")
            return

        # Получаем регион из выбранной записи в расписании
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите запись в расписании")
            return

        item = self.schedule_tree.item(selection[0])
        region = item['values'][0]  # Регион в первом столбце

        # Ищем данные для этого водителя в этом регионе (гибкий поиск)
        driver_data = None

        # Сначала пытаемся найти точное совпадение по имени и региону
        for key, driver_info in self.saved_drivers.items():
            if (driver_info.get('driver_name', key) == driver_name and
                driver_info.get('region') == region):
                driver_data = driver_info
                break

        # Если точное совпадение не найдено, ищем по имени водителя
        if not driver_data and driver_name:
            for key, driver_info in self.saved_drivers.items():
                if driver_info.get('driver_name', key) == driver_name:
                    driver_data = driver_info
                    break

        # Если и по имени не найдено, ищем по ключу (для обратной совместимости)
        if not driver_data and driver_name:
            if driver_name in self.saved_drivers:
                driver_data = self.saved_drivers[driver_name]

        if not driver_data:
            self.show_modern_message("Нет данных", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Получаем адреса водителя
        addresses = driver_data.get('addresses', [])

        if not addresses:
            self.show_modern_message("Нет адресов", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Счетчик удаленных маркеров
        removed_count = 0

        # Удаляем адреса с карты
        for address in addresses:
            # Проверяем, есть ли адрес на карте
            if address in self.markers:
                # Получаем маркер
                marker = self.markers[address]

                # Удаляем маркер с карты
                if marker in self.map_widget.canvas_marker_list:
                    marker.delete()
                    try:
                        self.map_widget.canvas_marker_list.remove(marker)
                    except ValueError:
                        pass

                # Удаляем из словаря маркеров
                del self.markers[address]

                # Увеличиваем счетчик
                removed_count += 1

        # Обновляем карту
        self.map_widget.update()

        if removed_count > 0:
            self.show_modern_message("Адреса скрыты", f"Удалено {removed_count} адресов водителя {driver_name} с карты", "success")
        else:
            self.show_modern_message("Нет адресов на карте", f"Адреса водителя {driver_name} не были отображены на карте", "info")

    def load_driver_addresses_for_editing(self):
        """Загружает адреса выбранного водителя в список для редактирования"""
        driver_name = self.get_selected_driver_from_schedule()
        if not driver_name:
            return

        # Проверяем, инициализирован ли словарь сохраненных водителей
        if not hasattr(self, 'saved_drivers'):
            messagebox.showinfo("Информация", "Список сохраненных водителей еще не инициализирован")
            return

        # Получаем регион из выбранной записи в расписании
        selection = self.schedule_tree.selection()
        if not selection:
            messagebox.showwarning("Предупреждение", "Выберите запись в расписании")
            return

        item = self.schedule_tree.item(selection[0])
        region = item['values'][0]  # Регион в первом столбце

        # Ищем данные для этого водителя в этом регионе (гибкий поиск)
        driver_data = None
        driver_key = None

        # Сначала пытаемся найти точное совпадение по имени и региону
        for key, driver_info in self.saved_drivers.items():
            if (driver_info.get('driver_name', key) == driver_name and
                driver_info.get('region') == region):
                driver_data = driver_info
                driver_key = key
                break

        # Если точное совпадение не найдено, ищем по имени водителя
        if not driver_data and driver_name:
            for key, driver_info in self.saved_drivers.items():
                if driver_info.get('driver_name', key) == driver_name:
                    driver_data = driver_info
                    driver_key = key
                    break

        # Если и по имени не найдено, ищем по ключу (для обратной совместимости)
        if not driver_data and driver_name:
            if driver_name in self.saved_drivers:
                driver_data = self.saved_drivers[driver_name]
                driver_key = driver_name

        if not driver_data:
            self.show_modern_message("Нет данных", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Получаем адреса водителя
        addresses = driver_data.get('addresses', [])

        if not addresses:
            self.show_modern_message("Нет адресов", f"У водителя {driver_name} нет сохраненных адресов", "info")
            return

        # Спрашиваем подтверждение
        if not messagebox.askyesno("Подтверждение",
                                  f"Загрузить {len(addresses)} адресов водителя {driver_name} в список для редактирования?"):
            return

        # Загружаем адреса в основной список
        for address in addresses:
            # Проверяем, есть ли адрес уже в списке
            exists = False
            for item_id in self.all_addresses_list.get_children():
                if self.all_addresses_list.item(item_id)['values'][0] == address:
                    exists = True
                    break

            if exists:
                continue

            # Геокодируем адрес
            lat, lng = self.geocode_address(address)
            if lat is None or lng is None:
                print(f"Не удалось найти координаты для адреса: {address}")
                continue

            # Создаем маркер
            marker = self.map_widget.set_marker(
                lat, lng,
                text=address,
                marker_color_circle="brown",
                marker_color_outside="brown"
            )

            # Добавляем маркер в словари
            self.markers[address] = marker
            self.original_markers[address] = marker

            # Получаем значения суммы и веса для адреса из Excel файла
            # Проверяем, есть ли адрес в address_values (т.е. был ли он загружен из Excel)
            if address in self.address_values:
                # Используем значения из Excel файла
                address_sum = self.address_values[address].get('sum', 0.0)
                address_weight = self.address_values[address].get('weight', 0.0)
                print(f"Используем значения из Excel для адреса {address}: сумма={address_sum:.2f}, вес={address_weight:.3f}")
            else:
                # Если адреса нет в address_values, пробуем получить значения из saved_drivers
                address_sum = 0.0
                address_weight = 0.0

                if driver_data:
                    # Получаем общую сумму и вес водителя
                    driver_sum = driver_data.get('sum', 0.0)
                    driver_weight = driver_data.get('weight', 0.0)

                    # Если есть общая сумма/вес и количество адресов, распределяем их равномерно
                    driver_addresses = driver_data.get('addresses', [])
                    if len(driver_addresses) > 0:
                        if driver_sum > 0:
                            address_sum = driver_sum / len(driver_addresses)
                            print(f"Распределяем сумму для адреса {address}: {address_sum:.2f}")

                        if driver_weight > 0:
                            address_weight = driver_weight / len(driver_addresses)
                            print(f"Распределяем вес для адреса {address}: {address_weight:.3f}")
                    else:
                        print(f"Нет адресов для распределения суммы и веса для водителя {driver_name}")

            # Добавляем адрес в список с суммой и весом
            self.all_addresses_list.insert('', 'end', values=(
                address,
                f"{address_sum:.2f}",  # Сумма с двумя десятичными знаками
                f"{address_weight:.3f}"  # Вес с тремя десятичными знаками
            ))

            # Обновляем значения в address_values
            self.address_values[address] = {
                'sum': address_sum,
                'weight': address_weight
            }

        # Обновляем счетчики после загрузки всех адресов
        self.update_counts()

        # Обнуляем информацию о количестве адресов и сумме в записи расписания
        if driver_key and driver_key in self.saved_drivers:
            # Сохраняем цвет и регион
            color = driver_data.get('color', '')
            region = driver_data.get('region', '')
            region_index = driver_data.get('region_index', None)

            # Обнуляем количество, сумму, вес и адреса
            self.saved_drivers[driver_key] = {
                'driver_name': driver_name,
                'color': color,
                'region': region,
                'region_index': region_index,
                'count': 0,
                'sum': 0.0,
                'weight': 0.0,
                'addresses': []
            }

            # Обновляем отображение расписания
            self.update_schedule_display()

            # Сохраняем изменения в файл
            self.save_schedule(show_message=False)
            self.save_drivers_addresses()

        self.show_modern_message("Адреса загружены", f"Загружено {len(addresses)} адресов водителя {driver_name} в список для редактирования. Информация о количестве адресов и сумме в расписании обнулена.", "success")

    def assign_driver_to_task(self):
        """Назначает выбранного водителя на новую задачу"""
        driver_name = self.get_selected_driver_from_schedule()
        if not driver_name:
            return

        # Открываем окно создания новой задачи с предварительно заполненным полем водителя
        self.create_task_with_driver(driver_name)

    def create_task_with_driver(self, driver_name):
        """Создает новую задачу с указанным водителем"""
        # Создаем диалоговое окно
        window = ttk.Toplevel(self.root)
        window.title("Новая задача")
        window.geometry("500x400")  # Увеличиваем высоту окна с 300 до 400
        window.transient(self.root)
        window.grab_set()

        # Центрируем окно на экране
        self.center_window(window)

        # Создаем фрейм для полей ввода
        frame = ttk.Frame(window, padding=10)
        frame.pack(fill=tk.BOTH, expand=True)

        # Поле для даты
        ttk.Label(frame, text="Дата:").grid(row=0, column=0, sticky=tk.W, pady=5)
        date_var = tk.StringVar(value=datetime.now().strftime("%d.%m.%Y"))
        date_entry = ttk.Entry(frame, textvariable=date_var)
        date_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для задачи (многострочное)
        ttk.Label(frame, text="Задача:").grid(row=1, column=0, sticky=tk.NW, pady=5)
        task_text = tk.Text(frame, height=3, width=40)
        task_text.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для водителя
        ttk.Label(frame, text="Водитель:").grid(row=2, column=0, sticky=tk.W, pady=5)
        driver_var = tk.StringVar(value=driver_name)
        driver_entry = ttk.Entry(frame, textvariable=driver_var)
        driver_entry.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для контрагента
        ttk.Label(frame, text="Контрагент:").grid(row=3, column=0, sticky=tk.W, pady=5)
        contractor_var = tk.StringVar()
        contractor_entry = ttk.Entry(frame, textvariable=contractor_var)
        contractor_entry.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=5)

        # Поле для адреса
        ttk.Label(frame, text="Адрес:").grid(row=4, column=0, sticky=tk.W, pady=5)
        address_var = tk.StringVar()
        address_entry = ttk.Entry(frame, textvariable=address_var)
        address_entry.grid(row=4, column=1, sticky=tk.EW, pady=5, padx=5)

        # Кнопка для выбора адреса и контрагента из списка
        ttk.Button(
            frame,
            text="Выбрать",
            width=10,
            command=lambda: self.select_address_for_task(address_var, contractor_var)
        ).grid(row=4, column=2, sticky=tk.W, pady=5, padx=5)

        # Поле для описания
        ttk.Label(frame, text="Описание:").grid(row=5, column=0, sticky=tk.NW, pady=5)
        description_text = tk.Text(frame, height=5, width=40)
        description_text.grid(row=5, column=1, sticky=tk.EW, pady=5, padx=5)

        # Настраиваем веса столбцов
        frame.columnconfigure(1, weight=1)

        # Кнопки сохранения и отмены
        button_frame = ttk.Frame(window)
        button_frame.pack(fill=tk.X, pady=10, padx=10)

        ttk.Button(
            button_frame,
            text="Сохранить",
            command=lambda: self.save_new_task(
                window,
                date_var.get(),
                task_text.get("1.0", "end-1c"),
                driver_var.get(),
                description_text.get("1.0", tk.END),
                contractor_var.get(),
                address_var.get()
            )
        ).pack(side=tk.RIGHT, padx=5)

        ttk.Button(
            button_frame,
            text="Отмена",
            command=window.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def save_new_task(self, window, date, task, driver, description, contractor, address):
        """Сохраняет новую задачу"""
        if not task:
            messagebox.showwarning("Предупреждение", "Введите задачу")
            return

        # Создаем новую задачу
        task_id = self.next_task_id
        self.tasks[task_id] = {
            'date': date,
            'task': task,
            'driver': driver,
            'description': description.strip(),
            'completed': False,
            'contractor': contractor,
            'address': address
        }

        # Увеличиваем счетчик ID
        self.next_task_id += 1

        # Обновляем заголовок фрейма
        self.tasks_frame.config(text=f"📋 Управление задачами ({len(self.tasks)})")

        # Обновляем отображение с сортировкой
        self.update_tasks_display()

        # Если указан адрес, отображаем его на карте
        if address:
            self.show_task_address_on_map(address, task_id)

            # Добавляем адрес и контрагента в реестр
            if (address, contractor) not in [(item['address'], item['contractor']) for item in self.task_addresses_registry]:
                self.task_addresses_registry.append({
                    'address': address,
                    'contractor': contractor
                })

        # Закрываем окно
        window.destroy()

        # Выводим сообщение
        self.show_modern_message("Задача создана", f"Задача '{task}' успешно добавлена", "success")

    def on_closing(self):
        """Обработчик закрытия окна"""
        print("Закрытие программы...")

        try:
            # Очищаем все маркеры с карты перед закрытием
            if hasattr(self, 'map_widget') and self.map_widget:
                try:
                    # Удаляем все маркеры
                    for marker in list(self.map_widget.canvas_marker_list):
                        try:
                            marker.delete()
                        except:
                            pass
                    self.map_widget.canvas_marker_list.clear()
                except:
                    pass
        except Exception as e:
            print(f"Ошибка при очистке маркеров: {e}")

        # Сохраняем кэш перед закрытием
        try:
            self.save_geocode_cache()
        except:
            pass

        # Сохраняем реестр водителей
        try:
            self.save_drivers_registry()
        except:
            pass

        # Сохраняем список задач
        try:
            self.save_tasks_list()
        except:
            pass

        # Сохраняем реестр адресов и контрагентов
        try:
            self.save_task_addresses_registry()
        except:
            pass

        # Сохраняем расписание водителей
        try:
            self.save_schedule(show_message=False)
        except:
            pass

        try:
            # Сохраняем адреса водителей
            self.save_drivers_addresses()
        except Exception as e:
            print(f"Ошибка при сохранении адресов водителей в on_closing: {e}")
            # Не показываем сообщение об ошибке при закрытии программы
            pass

        try:
            # Сохраняем значения суммы и веса для адресов
            self.save_address_values()
        except Exception as e:
            print(f"Ошибка при сохранении значений адресов в on_closing: {e}")
            # Не показываем сообщение об ошибке при закрытии программы
            pass

        # Принудительно очищаем все ссылки на изображения и виджеты
        try:
            # Очищаем все словари с маркерами
            if hasattr(self, 'markers'):
                self.markers.clear()
            if hasattr(self, 'original_markers'):
                self.original_markers.clear()
            if hasattr(self, 'selected_markers'):
                self.selected_markers.clear()
            if hasattr(self, 'selected_markers2'):
                self.selected_markers2.clear()

            # Очищаем все изображения в tkinter
            if hasattr(self, 'root'):
                try:
                    # Удаляем все изображения из памяти tkinter
                    for widget in self.root.winfo_children():
                        self._cleanup_widget_images(widget)
                except:
                    pass

            # Очищаем карту более агрессивно
            if hasattr(self, 'map_widget'):
                try:
                    # Очищаем все внутренние структуры карты
                    if hasattr(self.map_widget, 'canvas'):
                        self.map_widget.canvas.delete("all")
                    if hasattr(self.map_widget, 'canvas_marker_list'):
                        self.map_widget.canvas_marker_list.clear()
                    # Удаляем ссылку на карту
                    self.map_widget = None
                except:
                    pass

            # Принудительная сборка мусора несколько раз
            import gc
            for _ in range(3):
                gc.collect()

        except Exception as e:
            print(f"Ошибка при очистке ресурсов: {e}")

        # Подавляем stderr перед закрытием
        import sys
        original_stderr = sys.stderr

        class DevNull:
            def write(self, text): pass
            def flush(self): pass

        try:
            # Временно подавляем все ошибки
            sys.stderr = DevNull()

            # Закрываем окно более корректно
            try:
                self.root.quit()  # Используем quit() для корректного завершения mainloop
            except:
                try:
                    self.root.destroy()
                except:
                    pass

            # Небольшая задержка для завершения процессов
            import time
            time.sleep(0.2)

        finally:
            # Восстанавливаем stderr
            sys.stderr = original_stderr

    def _cleanup_widget_images(self, widget):
        """Рекурсивно очищает изображения из виджетов"""
        try:
            # Очищаем изображения из виджета, если они есть
            if hasattr(widget, 'image'):
                widget.image = None
            if hasattr(widget, 'photo'):
                widget.photo = None

            # Рекурсивно обрабатываем дочерние виджеты
            for child in widget.winfo_children():
                self._cleanup_widget_images(child)
        except:
            pass

    def save_drivers_addresses(self):
        """Сохраняет адреса водителей в JSON файл"""
        if not hasattr(self, 'saved_drivers'):
            print("Атрибут saved_drivers не существует")
            return

        if not self.saved_drivers:
            print("Словарь saved_drivers пуст")
            return

        print(f"Начинаем сохранение адресов водителей. Всего водителей: {len(self.saved_drivers)}")

        try:
            # Создаем копию словаря saved_drivers для сохранения
            drivers_data = {}

            for driver_name, info in self.saved_drivers.items():
                print(f"Обработка водителя: {driver_name}")

                # Пропускаем пустые словари
                if not info:
                    print(f"  Пустой словарь для водителя {driver_name}, пропускаем")
                    continue

                # Проверяем тип info
                if not isinstance(info, dict):
                    print(f"  Неверный тип данных для водителя {driver_name}: {type(info)}, пропускаем")
                    continue

                # Создаем новый словарь для данных водителя
                driver_info = {}

                # Копируем только сериализуемые данные
                if 'color' in info:
                    print(f"  Копируем цвет: {info['color']}")
                    driver_info['color'] = info['color']

                if 'count' in info:
                    print(f"  Копируем количество: {info['count']}")
                    driver_info['count'] = info['count']

                if 'sum' in info:
                    print(f"  Копируем сумму: {info['sum']}")
                    driver_info['sum'] = info['sum']

                if 'weight' in info:
                    print(f"  Копируем вес: {info['weight']}")
                    driver_info['weight'] = info['weight']

                if 'region' in info:
                    print(f"  Копируем регион: {info['region']}")
                    driver_info['region'] = info['region']

                if 'region_index' in info:
                    print(f"  Копируем индекс региона: {info['region_index']}")
                    driver_info['region_index'] = info['region_index']

                # Сохраняем имя водителя для правильного поиска
                if 'driver_name' in info:
                    print(f"  Копируем имя водителя: {info['driver_name']}")
                    driver_info['driver_name'] = info['driver_name']

                # Копируем адреса, если они есть
                if 'addresses' in info:
                    if info['addresses']:
                        try:
                            # Проверяем, что addresses можно преобразовать в список
                            addresses_list = list(info['addresses'])
                            print(f"  Копируем адреса: {len(addresses_list)} шт.")
                            driver_info['addresses'] = addresses_list
                        except Exception as addr_e:
                            print(f"  Ошибка при преобразовании адресов в список: {addr_e}")
                    else:
                        print("  Список адресов пуст")

                # Добавляем в словарь для сохранения только если есть данные
                if driver_info:
                    print(f"  Добавляем водителя {driver_name} в словарь для сохранения")
                    drivers_data[driver_name] = driver_info
                else:
                    print(f"  Нет данных для сохранения для водителя {driver_name}")

            # Проверяем, есть ли данные для сохранения
            if not drivers_data:
                print("Нет данных для сохранения")
                return

            # Сохраняем в JSON файл
            print(f"Сохраняем данные в файл: {len(drivers_data)} водителей")
            with open("saved_drivers_addresses.json", "w", encoding="utf-8") as f:
                json.dump(drivers_data, f, ensure_ascii=False, indent=4)

            print(f"Сохранено {len(drivers_data)} водителей с адресами")
            return True
        except Exception as e:
            print(f"Ошибка при сохранении адресов водителей: {e}")
            # Не показываем сообщение об ошибке, если мы в процессе закрытия программы
            if hasattr(self, 'root') and self.root.winfo_exists():
                messagebox.showerror("Ошибка", f"Не удалось сохранить адреса водителей:\n{str(e)}")
            return False

    def save_address_values(self):
        """Сохраняет значения суммы и веса для адресов в JSON файл"""
        if not hasattr(self, 'address_values') or not self.address_values:
            print("Нет сохраненных значений адресов")
            return

        try:
            # Создаем копию словаря address_values для сохранения
            address_data = {}

            for address, values in self.address_values.items():
                # Пропускаем пустые словари
                if not values:
                    continue

                # Создаем новый словарь для данных адреса
                address_info = {}

                # Копируем значения суммы и веса
                if 'sum' in values:
                    address_info['sum'] = values['sum']

                if 'weight' in values:
                    address_info['weight'] = values['weight']

                # Добавляем в словарь для сохранения только если есть данные
                if address_info:
                    address_data[address] = address_info

            # Проверяем, есть ли данные для сохранения
            if not address_data:
                print("Нет данных для сохранения")
                return

            # Сохраняем в JSON файл
            with open("address_values.json", "w", encoding="utf-8") as f:
                json.dump(address_data, f, ensure_ascii=False, indent=4)

            print(f"Сохранено значений для {len(address_data)} адресов")
            return True
        except Exception as e:
            print(f"Ошибка при сохранении значений адресов: {e}")
            # Не показываем сообщение об ошибке, если мы в процессе закрытия программы
            if hasattr(self, 'root') and self.root.winfo_exists():
                messagebox.showerror("Ошибка", f"Не удалось сохранить значения адресов:\n{str(e)}")
            return False

    def load_address_values(self):
        """Загружает значения суммы и веса для адресов из JSON файла"""
        if not os.path.exists("address_values.json"):
            print("Файл со значениями адресов не найден")
            # Инициализируем пустой словарь, если файла нет
            if not hasattr(self, 'address_values'):
                self.address_values = {}
            return

        # Проверяем размер файла
        if os.path.getsize("address_values.json") == 0:
            print("Файл со значениями адресов пуст")
            # Инициализируем пустой словарь, если файл пуст
            if not hasattr(self, 'address_values'):
                self.address_values = {}
            return

        try:
            # Загружаем данные из JSON файла
            with open("address_values.json", "r", encoding="utf-8") as f:
                address_data = json.load(f)

            # Инициализируем словарь, если его еще нет
            if not hasattr(self, 'address_values'):
                self.address_values = {}

            # Обновляем данные адресов
            for address, values in address_data.items():
                self.address_values[address] = values

            print(f"Загружены значения для {len(address_data)} адресов")
        except json.JSONDecodeError:
            print("Ошибка декодирования JSON: файл со значениями адресов поврежден")
            # Инициализируем пустой словарь, если файл поврежден
            if not hasattr(self, 'address_values'):
                self.address_values = {}
        except Exception as e:
            print(f"Ошибка при загрузке значений адресов: {e}")
            # Не показываем сообщение об ошибке, если файл пуст или поврежден
            if os.path.getsize("address_values.json") > 0:
                messagebox.showwarning("Предупреждение", f"Не удалось загрузить значения адресов:\n{str(e)}")

    def load_drivers_addresses(self):
        """Загружает адреса водителей из JSON файла"""
        if not os.path.exists("saved_drivers_addresses.json"):
            print("Файл с адресами водителей не найден")
            # Инициализируем пустой словарь, если файла нет
            if not hasattr(self, 'saved_drivers'):
                self.saved_drivers = {}
            return

        # Проверяем размер файла
        if os.path.getsize("saved_drivers_addresses.json") == 0:
            print("Файл с адресами водителей пуст")
            # Инициализируем пустой словарь, если файл пуст
            if not hasattr(self, 'saved_drivers'):
                self.saved_drivers = {}
            return

        try:
            # Загружаем данные из JSON файла
            with open("saved_drivers_addresses.json", "r", encoding="utf-8") as f:
                drivers_data = json.load(f)

            # Инициализируем словарь, если его еще нет
            if not hasattr(self, 'saved_drivers'):
                self.saved_drivers = {}

            # Обновляем данные водителей
            for driver_name, info in drivers_data.items():
                self.saved_drivers[driver_name] = info

            print(f"Загружено {len(drivers_data)} водителей с адресами")
        except json.JSONDecodeError:
            print("Ошибка декодирования JSON: файл с адресами водителей поврежден")
            # Инициализируем пустой словарь, если файл поврежден
            if not hasattr(self, 'saved_drivers'):
                self.saved_drivers = {}
        except Exception as e:
            print(f"Ошибка при загрузке адресов водителей: {e}")
            # Не показываем сообщение об ошибке, если файл пуст или поврежден
            if os.path.getsize("saved_drivers_addresses.json") > 0:
                messagebox.showwarning("Предупреждение", f"Не удалось загрузить адреса водителей:\n{str(e)}")

    # ===== МЕТОДЫ ДЛЯ АВТОМАТИЧЕСКОГО РАСПРЕДЕЛЕНИЯ АДРЕСОВ =====

    def show_distribution_settings(self):
        """Показывает окно настроек для автоматического распределения адресов"""
        # Проверяем, есть ли адреса для распределения
        if not self.markers:
            self.show_modern_message("Нет адресов", "Нет загруженных адресов для распределения", "warning")
            return

        # Создаем окно настроек
        settings_window = ttk.Toplevel(self.root)
        settings_window.title("Настройки автоматического распределения")
        settings_window.geometry("600x600")  # Увеличиваем высоту с 550 до 600
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Центрируем окно
        self.center_window(settings_window)

        # Основной фрейм
        main_frame = ttk.Frame(settings_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Заголовок
        title_label = ttk.Label(
            main_frame,
            text="🎯 Автоматическое распределение адресов",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack(pady=(0, 20))

        # Информация о количестве адресов
        info_label = ttk.Label(
            main_frame,
            text=f"Доступно адресов для распределения: {len(self.markers)}",
            font=("Segoe UI", 10)
        )
        info_label.pack(pady=(0, 15))

        # Настройки
        settings_frame = ttk.LabelFrame(main_frame, text="Параметры распределения", padding=15)
        settings_frame.pack(fill=tk.X, pady=(0, 15))

        # Размер группы
        group_frame = ttk.Frame(settings_frame)
        group_frame.pack(fill=tk.X, pady=5)
        ttk.Label(group_frame, text="Количество адресов в группе:").pack(side=tk.LEFT)
        group_size_var = tk.IntVar(value=8)
        group_spinbox = ttk.Spinbox(
            group_frame,
            from_=5,
            to=15,
            textvariable=group_size_var,
            width=10
        )
        group_spinbox.pack(side=tk.RIGHT)

        # Ширина сектора
        sector_frame = ttk.Frame(settings_frame)
        sector_frame.pack(fill=tk.X, pady=5)
        ttk.Label(sector_frame, text="Ширина сектора (градусы):").pack(side=tk.LEFT)
        sector_width_var = tk.DoubleVar(value=5.0)  # Уменьшаем до 5.0 для точности
        sector_spinbox = ttk.Spinbox(
            sector_frame,
            from_=5.0,
            to=30.0,
            increment=5.0,
            textvariable=sector_width_var,
            width=10
        )
        sector_spinbox.pack(side=tk.RIGHT)

        # Радиус поиска (множитель базового расстояния)
        radius_frame = ttk.Frame(settings_frame)
        radius_frame.pack(fill=tk.X, pady=5)
        ttk.Label(radius_frame, text="Множитель радиуса:").pack(side=tk.LEFT)
        radius_multiplier_var = tk.DoubleVar(value=1.2)  # Увеличиваем по умолчанию
        radius_spinbox = ttk.Spinbox(
            radius_frame,
            from_=0.5,
            to=3.0,  # Увеличиваем максимум до 3.0
            increment=0.1,
            textvariable=radius_multiplier_var,
            width=10
        )
        radius_spinbox.pack(side=tk.RIGHT)

        # Описание алгоритма
        desc_frame = ttk.LabelFrame(main_frame, text="Описание алгоритма", padding=15)
        desc_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        description = """🔄 Алгоритм распределения (ИСПРАВЛЕННАЯ ВЕРСИЯ):

1. Определяется базовая линия от центра Минска к конечной точке
2. Движение происходит строго против часовой стрелки
3. Адреса группируются по секторам заданной ширины
4. Остатки кластера назначаются следующему водителю в первую очередь
5. Цвета маркеров меняются для визуализации групп
6. Адреса автоматически добавляются в Список 1

⚙️ Настраиваемые параметры:
• Размер группы - количество адресов на одного водителя
• Ширина сектора - угол охвата для поиска адресов (по умолчанию 5°)
• Множитель радиуса - дальность поиска от центра

✅ ИСПРАВЛЕНИЯ:
• Уменьшена ширина сектора до 5° для предотвращения пропуска адресов
• Улучшен алгоритм выбора цветов для избежания дублирования
• Исправлена проблема с пропуском секторов при распределении"""

        desc_text = tk.Text(desc_frame, height=10, wrap=tk.WORD, font=("Segoe UI", 9))
        desc_text.pack(fill=tk.BOTH, expand=True)
        desc_text.insert(tk.END, description)
        desc_text.config(state=tk.DISABLED)

        # Кнопки
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(
            buttons_frame,
            text="Отмена",
            command=settings_window.destroy
        ).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(
            buttons_frame,
            text="Начать распределение",
            command=lambda: self.start_distribution(
                settings_window,
                group_size_var.get(),
                sector_width_var.get(),
                radius_multiplier_var.get()
            )
        ).pack(side=tk.RIGHT)

    def start_distribution(self, settings_window, group_size, sector_width, radius_multiplier):
        """Запускает процесс автоматического распределения адресов"""
        settings_window.destroy()

        # Проверяем, есть ли уже адреса в списке 1
        if self.selected_markers:
            self.show_modern_message(
                "Список не пуст",
                "Сначала очистите Список 1 или назначьте водителя для текущих адресов",
                "warning"
            )
            return

        # Константы для центра Минска и конечной точки
        MINSK_CENTER = (53.9015445, 27.5610349)
        END_POINT = (54.0145446, 27.8906118)

        # Вычисляем базовые параметры
        base_distance = self.calculate_distance(*MINSK_CENTER, *END_POINT)
        base_angle = self.calculate_bearing(*MINSK_CENTER, *END_POINT)
        max_radius = base_distance * radius_multiplier

        print(f"Базовое расстояние: {base_distance:.2f} км")
        print(f"Базовый угол: {base_angle:.2f}°")
        print(f"Максимальный радиус: {max_radius:.2f} км")

        # Получаем все адреса для распределения (исключаем уже выбранные)
        available_addresses = []
        for address, marker in self.markers.items():
            # Пропускаем адреса, которые уже в списках 1 или 2
            if address not in self.selected_markers and address not in self.selected_markers2:
                lat, lng = marker.position
                available_addresses.append({
                    'address': address,
                    'lat': lat,
                    'lng': lng,
                    'marker': marker
                })

        if not available_addresses:
            self.show_modern_message("Нет адресов", "Нет доступных адресов для распределения", "warning")
            return

        print(f"Доступно адресов для распределения: {len(available_addresses)}")

        # Инициализируем список цветов текущей сессии распределения
        self.current_session_colors = set()
        print(f"🎨 Инициализирован список цветов текущей сессии распределения")

        # Сохраняем параметры для продолжения распределения
        self.distribution_params = {
            'available_addresses': available_addresses,
            'center': MINSK_CENTER,
            'base_angle': base_angle,
            'max_radius': max_radius,
            'sector_width': sector_width,
            'group_size': group_size,
            'current_angle': base_angle,
            'remainder_from_previous': [],
            'last_cluster_center': None  # Для отслеживания центра предыдущего кластера
        }

        # Распределяем первую группу
        self.distribute_next_group()

    def distribute_next_group(self):
        """Распределяет следующую группу адресов и останавливается для назначения водителя"""
        if not hasattr(self, 'distribution_params'):
            self.show_modern_message("Ошибка", "Параметры распределения не найдены", "error")
            return

        params = self.distribution_params

        # ВАЖНО: Обновляем список доступных адресов перед каждым распределением
        print("🔄 Обновляем список доступных адресов перед распределением...")
        self.update_available_addresses_for_distribution()

        # Проверяем, есть ли еще адреса для распределения
        if not params['available_addresses'] and not params['remainder_from_previous']:
            self.show_modern_message(
                "Распределение завершено",
                "Все адреса распределены",
                "success"
            )
            # Очищаем параметры
            delattr(self, 'distribution_params')
            return

        print(f"\n--- Распределение следующей группы ---")
        print(f"Текущий угол: {params['current_angle']:.2f}°")
        print(f"Остатков от предыдущего кластера: {len(params['remainder_from_previous'])}")
        print(f"Оставшихся адресов: {len(params['available_addresses'])}")

        # Начинаем формировать группу с остатков предыдущего кластера
        current_group = params['remainder_from_previous'].copy()
        params['remainder_from_previous'] = []

        # Если группа уже полная, обрабатываем её
        if len(current_group) >= params['group_size']:
            group_to_process = current_group[:params['group_size']]
            params['remainder_from_previous'] = current_group[params['group_size']:]
            self.add_group_to_list1(group_to_process)
            return

        # Ищем адреса в секторах, пока не наберем нужное количество
        max_iterations = 20  # Защита от бесконечного цикла
        iteration = 0

        while len(current_group) < params['group_size'] and params['available_addresses'] and iteration < max_iterations:
            iteration += 1

            # Определяем границы текущего сектора
            start_angle = params['current_angle']
            end_angle = (params['current_angle'] - params['sector_width']) % 360

            print(f"Сектор {iteration}: {start_angle:.2f}° - {end_angle:.2f}°")

            # Находим адреса в текущем секторе с адаптивным радиусом
            sector_addresses = []
            addresses_to_remove = []

            # Начинаем с базового радиуса и увеличиваем при необходимости
            current_radius = params['max_radius']
            max_search_radius = params['max_radius'] * 2.5  # Максимальный радиус поиска

            while not sector_addresses and current_radius <= max_search_radius:
                for addr_info in params['available_addresses']:
                    if self.point_in_sector(
                        addr_info['lat'], addr_info['lng'],
                        params['center'][0], params['center'][1],
                        end_angle, start_angle,  # Меняем местами для движения против часовой
                        current_radius
                    ):
                        sector_addresses.append(addr_info)
                        addresses_to_remove.append(addr_info)

                # Если не нашли адреса, увеличиваем радиус
                if not sector_addresses:
                    current_radius *= 1.3
                    print(f"  Увеличиваем радиус поиска до {current_radius:.2f} км")

            # Удаляем найденные адреса из оставшихся
            for addr_info in addresses_to_remove:
                params['available_addresses'].remove(addr_info)

            print(f"Найдено адресов в секторе: {len(sector_addresses)} (радиус: {current_radius:.2f} км)")
            if sector_addresses:
                for addr_info in sector_addresses:
                    distance = self.calculate_distance(
                        params['center'][0], params['center'][1],
                        addr_info['lat'], addr_info['lng']
                    )
                    print(f"  - {addr_info['address']} (расстояние: {distance:.2f} км)")

            # ВАЖНО: Проверяем, нужно ли выбрать ближайшие из найденных в секторе
            if sector_addresses and current_group:
                # Если у нас уже есть адреса в группе, выбираем ближайшие к ним
                needed_count = params['group_size'] - len(current_group)
                if needed_count > 0 and len(sector_addresses) >= needed_count:
                    print(f"🔍 Выбираем {needed_count} ближайших из {len(sector_addresses)} найденных в секторе...")

                    # Вычисляем расстояния от каждого адреса сектора до каждого адреса в группе
                    distances_to_group = []
                    for addr_info in sector_addresses:
                        min_distance = float('inf')
                        closest_group_addr = None

                        for group_addr in current_group:
                            distance = self.calculate_distance(
                                group_addr['lat'], group_addr['lng'],
                                addr_info['lat'], addr_info['lng']
                            )
                            if distance < min_distance:
                                min_distance = distance
                                closest_group_addr = group_addr['address']

                        distances_to_group.append((min_distance, addr_info, closest_group_addr))

                    # Сортируем по расстоянию и берем ближайшие
                    distances_to_group.sort(key=lambda x: x[0])
                    closest_addresses = distances_to_group[:needed_count]

                    print(f"🎯 Выбраны ближайшие к группе:")
                    selected_addresses = []
                    for min_distance, addr_info, closest_to in closest_addresses:
                        print(f"  ✅ {addr_info['address']} (расстояние: {min_distance:.2f} км до {closest_to})")
                        selected_addresses.append(addr_info)

                    # Добавляем выбранные адреса к группе
                    current_group.extend(selected_addresses)
                else:
                    # Если нужно меньше адресов чем найдено, или группа пустая - берем все
                    current_group.extend(sector_addresses)
            else:
                # Если группа пустая или в секторе нет адресов - берем все найденные
                current_group.extend(sector_addresses)

            # Переходим к следующему сектору (против часовой стрелки)
            params['current_angle'] = (params['current_angle'] - params['sector_width']) % 360

            # Если группа уже полная, прерываем цикл
            if len(current_group) >= params['group_size']:
                print(f"🔄 Группа полная ({len(current_group)}/{params['group_size']}), прерываем поиск")
                print(f"🔄 Угол после обновления в цикле: {params['current_angle']:.2f}°")
                break

            # Если прошли полный круг и ничего не нашли
            if abs(params['current_angle'] - params['base_angle']) < 1 and not sector_addresses:
                print("Прошли полный круг без находок, увеличиваем радиус")
                params['max_radius'] *= 1.5  # Увеличиваем радиус поиска
                if params['max_radius'] > 100:  # Ограничение в 100 км
                    print("Достигнут максимальный радиус")
                    break

        # Обрабатываем собранную группу
        if current_group:
            if len(current_group) >= params['group_size']:
                # Если группа больше нужного размера, сохраняем остаток
                group_to_process = current_group[:params['group_size']]
                params['remainder_from_previous'] = current_group[params['group_size']:]

                # ВАЖНО: Угол уже правильно обновился в цикле
                # НЕ нужно дополнительно обновлять угол!
                print(f"🔄 Группа полная, угол для следующего распределения: {params['current_angle']:.2f}° (ИСПРАВЛЕНО - без пропуска секторов)")
            else:
                # Если группа меньше нужного размера, ищем дополнительные адреса в следующих секторах
                group_to_process = current_group
                needed_count = params['group_size'] - len(current_group)

                if needed_count > 0 and params['available_addresses']:
                    print(f"🔍 Группа неполная ({len(current_group)}/{params['group_size']}), ищем {needed_count} ближайших адресов в следующих секторах...")

                    # Сохраняем центр неполного кластера для поиска ближайших
                    if current_group:
                        incomplete_cluster_center = (
                            sum(addr['lat'] for addr in current_group) / len(current_group),
                            sum(addr['lng'] for addr in current_group) / len(current_group)
                        )
                        print(f"📍 Центр неполного кластера: {incomplete_cluster_center[0]:.6f}, {incomplete_cluster_center[1]:.6f}")
                    else:
                        incomplete_cluster_center = params.get('last_cluster_center', params['center'])

                    # Ищем адреса в следующих секторах
                    additional_addresses = []
                    search_angle = params['current_angle']
                    sectors_searched = 0
                    max_sectors_to_search = 12  # Максимум секторов для поиска (полный круг)

                    while len(additional_addresses) < needed_count and sectors_searched < max_sectors_to_search and params['available_addresses']:
                        # Переходим к следующему сектору
                        search_angle = (search_angle - params['sector_width']) % 360
                        sectors_searched += 1

                        # Вычисляем границы сектора
                        start_angle = search_angle
                        end_angle = (search_angle + params['sector_width']) % 360

                        print(f"🔍 Ищем в секторе {sectors_searched}: {start_angle:.1f}° - {end_angle:.1f}°")

                        # Находим адреса в текущем секторе поиска
                        sector_candidates = []
                        current_radius = params['max_radius']
                        max_search_radius = params['max_radius'] * 2.5

                        while not sector_candidates and current_radius <= max_search_radius:
                            for addr_info in params['available_addresses']:
                                if self.point_in_sector(
                                    addr_info['lat'], addr_info['lng'],
                                    params['center'][0], params['center'][1],
                                    end_angle, start_angle,  # Меняем местами для движения против часовой
                                    current_radius
                                ):
                                    sector_candidates.append(addr_info)

                            if not sector_candidates:
                                current_radius *= 1.3
                                print(f"    Увеличиваем радиус поиска до {current_radius:.2f} км")

                        if sector_candidates:
                            print(f"    Найдено {len(sector_candidates)} кандидатов в секторе")

                            # Из найденных в секторе выбираем ближайшие к неполному кластеру
                            distances_to_cluster = []
                            for addr_info in sector_candidates:
                                # Находим минимальное расстояние до любого адреса в неполном кластере
                                if current_group:
                                    min_distance = float('inf')
                                    closest_group_addr = None
                                    for group_addr in current_group:
                                        distance = self.calculate_distance(
                                            group_addr['lat'], group_addr['lng'],
                                            addr_info['lat'], addr_info['lng']
                                        )
                                        if distance < min_distance:
                                            min_distance = distance
                                            closest_group_addr = group_addr['address']
                                    distances_to_cluster.append((min_distance, addr_info, closest_group_addr))
                                else:
                                    # Если группа пустая, используем расстояние до центра
                                    distance = self.calculate_distance(
                                        incomplete_cluster_center[0], incomplete_cluster_center[1],
                                        addr_info['lat'], addr_info['lng']
                                    )
                                    distances_to_cluster.append((distance, addr_info, "центр"))

                            # Сортируем по расстоянию до кластера
                            distances_to_cluster.sort(key=lambda x: x[0])

                            # Берем нужное количество ближайших
                            remaining_needed = needed_count - len(additional_addresses)
                            closest_from_sector = distances_to_cluster[:remaining_needed]

                            for distance, addr_info, closest_to in closest_from_sector:
                                print(f"    ✅ {addr_info['address']} (расстояние: {distance:.2f} км до {closest_to})")
                                additional_addresses.append(addr_info)
                                params['available_addresses'].remove(addr_info)

                    # Добавляем найденные адреса к группе
                    group_to_process.extend(additional_addresses)
                    print(f"🎯 Добавлено {len(additional_addresses)} адресов из следующих секторов")

                # Сохраняем центр кластера для следующего распределения
                if group_to_process:
                    params['last_cluster_center'] = (
                        sum(addr['lat'] for addr in group_to_process) / len(group_to_process),
                        sum(addr['lng'] for addr in group_to_process) / len(group_to_process)
                    )
                    print(f"💾 Сохранен центр кластера для следующего распределения: {params['last_cluster_center'][0]:.6f}, {params['last_cluster_center'][1]:.6f}")

            self.add_group_to_list1(group_to_process)
        else:
            self.show_modern_message(
                "Нет адресов",
                "Не найдено адресов для следующей группы",
                "warning"
            )
            # Очищаем параметры
            delattr(self, 'distribution_params')

    def add_group_to_list1(self, address_group):
        """Добавляет группу адресов в Список 1 с новым цветом"""
        if not address_group:
            return

        print(f"Добавляем в Список 1: {len(address_group)} адресов")
        print(f"Текущее состояние selected_markers: {len(self.selected_markers)} адресов")
        print(f"Текущее состояние списка 1: {self.selected_addresses_list.size()} элементов")

        # Получаем новый цвет для группы
        color = self.get_next_available_color()

        # ВАЖНО: Добавляем цвет в список текущей сессии СРАЗУ после получения
        if not hasattr(self, 'current_session_colors'):
            self.current_session_colors = set()

        self.current_session_colors.add(color)
        print(f"✅ Цвет {color} добавлен в current_session_colors")

        print(f"🎨 Назначен цвет для группы: {color}")
        print(f"📝 Цвета текущей сессии: {sorted(self.current_session_colors)}")

        # Меняем цвет маркеров и добавляем в список 1
        for addr_info in address_group:
            address = addr_info['address']
            old_marker = addr_info['marker']

            try:
                print(f"Обрабатываем адрес: {address}")

                # Получаем координаты из старого маркера
                lat, lng = old_marker.position
                print(f"  Координаты: {lat}, {lng}")

                # Удаляем адрес из основного списка (как в обычном add_to_selected)
                for item in self.all_addresses_list.get_children():
                    item_values = self.all_addresses_list.item(item)['values']
                    if item_values and item_values[0] == address:
                        self.all_addresses_list.delete(item)
                        print(f"  Удален из основного списка")
                        break

                # Удаляем старый маркер с карты (безопасно)
                try:
                    # Проверяем, что маркер существует и имеет метод delete
                    if hasattr(old_marker, 'delete') and callable(getattr(old_marker, 'delete')):
                        if old_marker in self.map_widget.canvas_marker_list:
                            old_marker.delete()
                            self.map_widget.canvas_marker_list.remove(old_marker)
                            print(f"  Удален старый маркер с карты")
                        else:
                            # Маркер не в списке, но все равно пытаемся удалить
                            old_marker.delete()
                            print(f"  Удален старый маркер (не был в списке карты)")
                    else:
                        print(f"  Старый маркер недействителен, пропускаем удаление")
                except Exception as marker_error:
                    print(f"  Предупреждение: не удалось удалить старый маркер: {marker_error}")
                    # Продолжаем выполнение, не прерывая процесс

                # Создаем новый маркер с новым цветом
                new_marker = self.map_widget.set_marker(
                    lat, lng,
                    text=address,
                    marker_color_circle=color,
                    marker_color_outside=color,
                    text_color=color
                )
                print(f"  Создан новый маркер с цветом {color}")
                print(f"  Новый маркер: {new_marker}")
                print(f"  Позиция нового маркера: {new_marker.position if new_marker else 'None'}")

                # Обновляем ссылки
                self.markers[address] = new_marker
                self.selected_markers[address] = new_marker
                print(f"  Обновлены ссылки в markers и selected_markers")
                print(f"  Размер markers: {len(self.markers)}")
                print(f"  Размер selected_markers: {len(self.selected_markers)}")

                # Добавляем значения адреса, если их нет
                if address not in self.address_values:
                    self.address_values[address] = {'sum': 0.0, 'weight': 0.0}
                    print(f"  Добавлены значения по умолчанию для адреса")

                # Добавляем в список 1
                self.selected_addresses_list.insert(tk.END, address)
                print(f"  Добавлен в selected_addresses_list")
                print(f"  Размер selected_addresses_list: {self.selected_addresses_list.size()}")

                print(f"  ✅ {address} -> цвет {color}")

            except Exception as e:
                print(f"❌ Ошибка при обработке адреса {address}: {e}")
                import traceback
                traceback.print_exc()

        print(f"После добавления:")
        print(f"  selected_markers: {len(self.selected_markers)} адресов")
        print(f"  selected_addresses_list: {self.selected_addresses_list.size()} элементов")

        # Обновляем счетчики и интерфейс
        self.update_counts()
        self.map_widget.update()

        # Принудительно обновляем интерфейс
        self.root.update_idletasks()
        self.root.update()

        # Показываем сообщение пользователю
        remaining_count = len(self.distribution_params['available_addresses']) + len(self.distribution_params['remainder_from_previous']) if hasattr(self, 'distribution_params') else 0

        self.show_modern_message(
            "Группа готова",
            f"В Список 1 добавлено {len(address_group)} адресов.\n"
            f"Назначьте водителя и сохраните в расписание.\n"
            f"Осталось адресов: {remaining_count}",
            "info"
        )

        # Изменяем текст кнопки "Распределить" на "Продолжить"
        if hasattr(self, 'distribution_params') and remaining_count > 0:
            # Находим кнопку и меняем текст
            for widget in self.driver_frame.winfo_children():
                if isinstance(widget, ttk.Button) and widget.cget('text') == 'Распределить':
                    widget.config(text='Продолжить', command=self.continue_distribution)
                    break

    def continue_distribution(self):
        """Продолжает распределение следующей группы"""
        # Проверяем, что список 1 пуст (водитель назначен и сохранен)
        if self.selected_markers:
            self.show_modern_message(
                "Список не пуст",
                "Сначала назначьте водителя и сохраните текущие адреса в расписание",
                "warning"
            )
            return

        # Сбрасываем кнопку на "Распределить"
        self.reset_distribution_button()

        # Продолжаем распределение
        self.distribute_next_group()

    def reset_distribution_button(self):
        """Сбрасывает кнопку обратно на "Распределить" """
        for widget in self.driver_frame.winfo_children():
            if isinstance(widget, ttk.Button) and widget.cget('text') in ['Продолжить', 'Распределить']:
                widget.config(text='Распределить', command=self.show_distribution_settings)
                break

    def check_distribution_after_save(self):
        """Проверяет состояние распределения после сохранения водителя"""
        # После сохранения проверяем, есть ли активное распределение
        if hasattr(self, 'distribution_params'):
            print("🔄 Проверяем состояние распределения после сохранения водителя...")

            # Обновляем список доступных адресов
            self.update_available_addresses_for_distribution()

            remaining_count = len(self.distribution_params['available_addresses']) + len(self.distribution_params['remainder_from_previous'])
            print(f"📊 Осталось адресов для распределения: {remaining_count}")

            if remaining_count > 0:
                # Меняем кнопку на "Продолжить"
                for widget in self.driver_frame.winfo_children():
                    if isinstance(widget, ttk.Button) and widget.cget('text') == 'Распределить':
                        widget.config(text='Продолжить', command=self.continue_distribution)
                        print("🔄 Кнопка изменена на 'Продолжить'")
                        break
            else:
                print("✅ Все адреса распределены")
        else:
            print("ℹ️ Распределение не активно")

    def update_available_addresses_for_distribution(self):
        """Обновляет список доступных адресов для распределения после ручных изменений"""
        if not hasattr(self, 'distribution_params'):
            return

        print("🔄 Обновляем список доступных адресов после ручных изменений...")

        # Получаем текущий список всех адресов, которые не в списках 1 и 2
        current_available = []
        for address, marker in self.markers.items():
            # Пропускаем адреса, которые уже в списках 1 или 2
            if address not in self.selected_markers and address not in self.selected_markers2:
                try:
                    lat, lng = marker.position
                    current_available.append({
                        'address': address,
                        'lat': lat,
                        'lng': lng,
                        'marker': marker
                    })
                    print(f"  ✅ Доступен: {address}")
                except Exception as e:
                    print(f"  ❌ Ошибка с маркером для {address}: {e}")

        # Сохраняем старые адреса для сравнения
        old_addresses = set(addr_info['address'] for addr_info in self.distribution_params['available_addresses'])
        new_addresses = set(addr_info['address'] for addr_info in current_available)

        # Обновляем список в параметрах распределения
        old_count = len(self.distribution_params['available_addresses'])
        self.distribution_params['available_addresses'] = current_available
        new_count = len(current_available)

        print(f"📊 Было доступных адресов: {old_count}")
        print(f"📊 Стало доступных адресов: {new_count}")

        # Показываем детали изменений
        added_addresses = new_addresses - old_addresses
        removed_addresses = old_addresses - new_addresses

        if added_addresses:
            print(f"➕ Добавлено {len(added_addresses)} адресов:")
            for addr in added_addresses:
                print(f"   + {addr}")

        if removed_addresses:
            print(f"➖ Удалено {len(removed_addresses)} адресов:")
            for addr in removed_addresses:
                print(f"   - {addr}")

        if not added_addresses and not removed_addresses:
            print("🔄 Изменений в списке адресов нет")

    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """Вычисляет расстояние между двумя точками в километрах (формула гаверсинуса)"""
        import math

        # Радиус Земли в километрах
        R = 6371.0

        # Преобразуем градусы в радианы
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Разности координат
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad

        # Формула гаверсинуса
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))

        return R * c

    def calculate_bearing(self, lat1, lon1, lat2, lon2):
        """Вычисляет азимут (направление) от точки 1 к точке 2 в градусах"""
        import math

        # Преобразуем градусы в радианы
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Разность долгот
        dlon = lon2_rad - lon1_rad

        # Вычисляем азимут
        y = math.sin(dlon) * math.cos(lat2_rad)
        x = math.cos(lat1_rad) * math.sin(lat2_rad) - math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon)

        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)

        # Нормализуем к диапазону 0-360
        return (bearing_deg + 360) % 360

    def point_in_sector(self, point_lat, point_lon, center_lat, center_lon,
                       start_angle, end_angle, max_distance):
        """Проверяет, находится ли точка в заданном секторе"""
        # Вычисляем расстояние от центра до точки
        distance = self.calculate_distance(center_lat, center_lon, point_lat, point_lon)

        # Проверяем расстояние
        if distance > max_distance:
            return False

        # Вычисляем угол от центра к точке
        bearing = self.calculate_bearing(center_lat, center_lon, point_lat, point_lon)

        # Нормализуем углы
        start_angle = start_angle % 360
        end_angle = end_angle % 360

        # Проверяем, находится ли угол в секторе
        if start_angle <= end_angle:
            return start_angle <= bearing <= end_angle
        else:
            # Сектор пересекает 0 градусов
            return bearing >= start_angle or bearing <= end_angle



if __name__ == "__main__":
    # Простое и надежное решение для подавления ошибок PIL
    import sys
    import os

    # Создаем главное окно с современной темой
    root = ttk.Window(themename="superhero")  # Современная темная тема
    app = MapApp(root)

    # Запускаем приложение
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Приложение прервано пользователем")
    except Exception as e:
        print(f"Ошибка в главном цикле: {e}")

    # Примечание: Ошибки PhotoImage при закрытии приложения являются известной
    # проблемой библиотеки PIL/Pillow и не влияют на функциональность программы.
    # Они возникают при освобождении ресурсов изображений после завершения tkinter
    # и могут быть безопасно проигнорированы.