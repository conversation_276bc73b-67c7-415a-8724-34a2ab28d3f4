import tkinter as tk
from tkinter import ttk

class MainWindow(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Сеточный layout")
        self.geometry("800x600")

        # 1. Верхняя панель (0-я строка)
        self.top_frame = ttk.Frame(self, height=50)
        self.top_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        # 2. Левая панель (1-я строка, 0-й столбец)
        self.left_frame = ttk.Frame(self, width=200, relief=tk.GROOVE)
        self.left_frame.grid(row=1, column=0, sticky="ns", padx=5, pady=5)
        self.left_frame.pack_propagate(False)

        # 3. Правая панель (1-я строка, 1-й столбец)
        self.right_frame = ttk.Frame(self, relief=tk.GROOVE)
        self.right_frame.grid(row=1, column=1, sticky="nsew", padx=5, pady=5)

        # Настройка растягивания
        self.grid_rowconfigure(1, weight=1)  # Растягиваем по вертикали
        self.grid_columnconfigure(1, weight=1)  # Растягиваем по горизонтали

        # Добавляем элементы
        ttk.Button(self.top_frame, text="Файл").pack(side=tk.LEFT)
        ttk.Label(self.left_frame, text="Меню").pack(pady=20)
        ttk.Label(self.right_frame, text="Контент").pack(pady=50)

if __name__ == "__main__":
    app = MainWindow()
    app.mainloop()