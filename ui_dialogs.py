"""
Модуль UI диалогов
Содержит различные диалоговые окна для взаимодействия с пользователем
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Callable, Optional, Dict, Any


class ModernDialog:
    """Базовый класс для современных диалогов"""

    def __init__(self, parent, title: str, width: int = 400, height: int = 300):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{width}x{height}")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Центрируем диалог
        self.dialog.geometry("+{}+{}".format(
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        # Настройка стиля
        self.setup_style()

    def setup_style(self):
        """Настройка современного стиля диалога"""
        self.dialog.configure(bg='#f0f0f0')

    def wait_for_result(self):
        """Ожидает результат диалога"""
        self.dialog.wait_window()
        return self.result


class AddressSelectionDialog(ModernDialog):
    """Диалог для выбора адресов для включения в распределение"""

    def __init__(self, parent, manually_removed_addresses: List[str],
                 on_apply: Callable[[List[str]], None]):
        super().__init__(parent, "Включить адреса в распределение", 600, 400)
        self.manually_removed_addresses = manually_removed_addresses
        self.on_apply = on_apply
        self.checkbox_states = {}

        self.create_widgets()

    def create_widgets(self):
        """Создает виджеты диалога"""
        # Заголовок
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(
            header_frame,
            text="Найдены адреса, убранные вручную из предыдущих групп",
            font=("Segoe UI", 12, "bold")
        ).pack()

        ttk.Label(
            header_frame,
            text="Выберите адреса для включения в дальнейшее распределение:",
            font=("Segoe UI", 10)
        ).pack(pady=(5, 0))

        # Список адресов с чекбоксами
        list_frame = ttk.Frame(self.dialog)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Создаем Treeview для списка адресов
        columns = ("select", "address")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=10)
        self.tree.heading("select", text="Включить")
        self.tree.heading("address", text="Адрес")
        self.tree.column("select", width=80, anchor="center")
        self.tree.column("address", width=400)

        # Добавляем скроллбар
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Добавляем адреса в список
        for address in self.manually_removed_addresses:
            item_id = self.tree.insert('', 'end', values=("☐", address))
            self.checkbox_states[item_id] = False

        # Привязываем события
        self.tree.bind('<Button-1>', self.toggle_checkbox)
        self.tree.bind('<Double-1>', self.toggle_checkbox)

        # Кнопки управления
        self.create_control_buttons()

        # Кнопки действий
        self.create_action_buttons()

    def create_control_buttons(self):
        """Создает кнопки управления выбором"""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Выбрать все", command=self.select_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Снять все", command=self.deselect_all).pack(side=tk.LEFT, padx=5)

    def create_action_buttons(self):
        """Создает кнопки действий"""
        action_frame = ttk.Frame(self.dialog)
        action_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(action_frame, text="Применить", command=self.apply_selection).pack(side=tk.RIGHT, padx=5)
        ttk.Button(action_frame, text="Отмена", command=self.cancel_selection).pack(side=tk.RIGHT, padx=5)

    def toggle_checkbox(self, event):
        """Переключает состояние чекбокса"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            current_state = self.checkbox_states[item]
            new_state = not current_state
            self.checkbox_states[item] = new_state

            # Обновляем отображение
            values = list(self.tree.item(item, 'values'))
            values[0] = "☑" if new_state else "☐"
            self.tree.item(item, values=values)

    def select_all(self):
        """Выбрать все адреса"""
        for item_id in self.checkbox_states:
            self.checkbox_states[item_id] = True
            values = list(self.tree.item(item_id, 'values'))
            values[0] = "☑"
            self.tree.item(item_id, values=values)

    def deselect_all(self):
        """Снять выбор со всех адресов"""
        for item_id in self.checkbox_states:
            self.checkbox_states[item_id] = False
            values = list(self.tree.item(item_id, 'values'))
            values[0] = "☐"
            self.tree.item(item_id, values=values)

    def apply_selection(self):
        """Применяет выбор и закрывает диалог"""
        selected_addresses = []
        for item_id, is_selected in self.checkbox_states.items():
            if is_selected:
                values = self.tree.item(item_id, 'values')
                selected_addresses.append(values[1])  # Адрес

        self.result = selected_addresses
        self.on_apply(selected_addresses)
        self.dialog.destroy()

    def cancel_selection(self):
        """Отменяет выбор и закрывает диалог"""
        self.result = []
        self.dialog.destroy()


class DistributionSettingsDialog(ModernDialog):
    """Диалог настроек распределения"""

    def __init__(self, parent, current_settings: Dict[str, Any] = None):
        super().__init__(parent, "Настройки распределения", 500, 400)
        self.current_settings = current_settings or {}
        self.create_widgets()

    def create_widgets(self):
        """Создает виджеты диалога настроек"""
        # Заголовок
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(
            header_frame,
            text="Настройки алгоритма распределения",
            font=("Segoe UI", 14, "bold")
        ).pack()

        # Основная область настроек
        settings_frame = ttk.Frame(self.dialog)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Максимальный радиус
        ttk.Label(settings_frame, text="Максимальный радиус (км):").grid(row=0, column=0, sticky="w", pady=5)
        self.radius_var = tk.StringVar(value=str(self.current_settings.get('max_radius', 15)))
        ttk.Entry(settings_frame, textvariable=self.radius_var, width=10).grid(row=0, column=1, sticky="w", padx=10)

        # Ширина сектора
        ttk.Label(settings_frame, text="Ширина сектора (градусы):").grid(row=1, column=0, sticky="w", pady=5)
        self.sector_var = tk.StringVar(value=str(self.current_settings.get('sector_width', 30)))
        ttk.Entry(settings_frame, textvariable=self.sector_var, width=10).grid(row=1, column=1, sticky="w", padx=10)

        # Размер группы
        ttk.Label(settings_frame, text="Размер группы:").grid(row=2, column=0, sticky="w", pady=5)
        self.group_size_var = tk.StringVar(value=str(self.current_settings.get('group_size', 8)))
        ttk.Entry(settings_frame, textvariable=self.group_size_var, width=10).grid(row=2, column=1, sticky="w", padx=10)

        # Кнопки
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Button(button_frame, text="OK", command=self.apply_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Отмена", command=self.cancel_settings).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="По умолчанию", command=self.reset_defaults).pack(side=tk.LEFT, padx=5)

    def apply_settings(self):
        """Применяет настройки"""
        try:
            self.result = {
                'max_radius': float(self.radius_var.get()),
                'sector_width': float(self.sector_var.get()),
                'group_size': int(self.group_size_var.get())
            }
            self.dialog.destroy()
        except ValueError:
            messagebox.showerror("Ошибка", "Некорректные значения настроек")

    def cancel_settings(self):
        """Отменяет изменения"""
        self.result = None
        self.dialog.destroy()

    def reset_defaults(self):
        """Сбрасывает к значениям по умолчанию"""
        self.radius_var.set("15")
        self.sector_var.set("30")
        self.group_size_var.set("8")


class ModernMessageBox:
    """Современные диалоги сообщений"""

    @staticmethod
    def show_info(parent, title: str, message: str):
        """Показывает информационное сообщение"""
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.geometry("400x200")
        dialog.transient(parent)
        dialog.grab_set()

        # Центрируем диалог
        dialog.geometry("+{}+{}".format(
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))

        # Содержимое
        frame = ttk.Frame(dialog)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(frame, text=title, font=("Segoe UI", 12, "bold")).pack(pady=(0, 10))
        ttk.Label(frame, text=message, wraplength=350).pack(pady=(0, 20))

        ttk.Button(frame, text="OK", command=dialog.destroy).pack()

        dialog.wait_window()

    @staticmethod
    def show_warning(parent, title: str, message: str):
        """Показывает предупреждение"""
        ModernMessageBox.show_info(parent, title, message)

    @staticmethod
    def show_error(parent, title: str, message: str):
        """Показывает ошибку"""
        ModernMessageBox.show_info(parent, title, message)


class DriverSaveDialog(ModernDialog):
    """Диалог сохранения водителя"""

    def __init__(self, parent, addresses: List[str], total_sum: float, total_weight: float):
        super().__init__(parent, "Сохранить водителя", 500, 300)
        self.addresses = addresses
        self.total_sum = total_sum
        self.total_weight = total_weight
        self.create_widgets()

    def create_widgets(self):
        """Создает виджеты диалога"""
        # Заголовок
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(
            header_frame,
            text="Сохранение водителя с адресами",
            font=("Segoe UI", 12, "bold")
        ).pack()

        # Информация
        info_frame = ttk.Frame(self.dialog)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(info_frame, text=f"Количество адресов: {len(self.addresses)}").pack(anchor="w")
        ttk.Label(info_frame, text=f"Общая сумма: {self.total_sum:.2f}").pack(anchor="w")
        ttk.Label(info_frame, text=f"Общий вес: {self.total_weight:.3f}").pack(anchor="w")

        # Поле ввода водителя
        driver_frame = ttk.Frame(self.dialog)
        driver_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(driver_frame, text="Имя водителя:").pack(anchor="w")
        self.driver_entry = ttk.Entry(driver_frame, width=40)
        self.driver_entry.pack(fill=tk.X, pady=5)
        self.driver_entry.focus()

        # Кнопки
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Button(button_frame, text="Сохранить", command=self.save_driver).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Отмена", command=self.cancel).pack(side=tk.RIGHT, padx=5)

        # Привязка Enter
        self.driver_entry.bind('<Return>', lambda e: self.save_driver())

    def save_driver(self):
        """Сохраняет водителя"""
        driver_name = self.driver_entry.get().strip()
        if not driver_name:
            ModernMessageBox.show_warning(self.dialog, "Предупреждение", "Введите имя водителя")
            return

        self.result = driver_name
        self.dialog.destroy()

    def cancel(self):
        """Отменяет сохранение"""
        self.result = None
        self.dialog.destroy()


class TaskDialog(ModernDialog):
    """Диалог создания/редактирования задачи"""

    def __init__(self, parent, task_data: Dict = None):
        title = "Редактировать задачу" if task_data else "Создать задачу"
        super().__init__(parent, title, 600, 500)
        self.task_data = task_data or {}
        self.create_widgets()

    def create_widgets(self):
        """Создает виджеты диалога"""
        # Заголовок
        header_frame = ttk.Frame(self.dialog)
        header_frame.pack(fill=tk.X, padx=20, pady=10)

        title = "Редактирование задачи" if self.task_data else "Создание новой задачи"
        ttk.Label(
            header_frame,
            text=title,
            font=("Segoe UI", 12, "bold")
        ).pack()

        # Основная форма
        form_frame = ttk.Frame(self.dialog)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Дата
        ttk.Label(form_frame, text="Дата:").grid(row=0, column=0, sticky="w", pady=5)
        self.date_entry = ttk.Entry(form_frame, width=40)
        self.date_entry.grid(row=0, column=1, sticky="ew", padx=10, pady=5)
        self.date_entry.insert(0, self.task_data.get('date', ''))

        # Задача
        ttk.Label(form_frame, text="Задача:").grid(row=1, column=0, sticky="w", pady=5)
        self.task_entry = ttk.Entry(form_frame, width=40)
        self.task_entry.grid(row=1, column=1, sticky="ew", padx=10, pady=5)
        self.task_entry.insert(0, self.task_data.get('task', ''))

        # Водитель
        ttk.Label(form_frame, text="Водитель:").grid(row=2, column=0, sticky="w", pady=5)
        self.driver_entry = ttk.Entry(form_frame, width=40)
        self.driver_entry.grid(row=2, column=1, sticky="ew", padx=10, pady=5)
        self.driver_entry.insert(0, self.task_data.get('driver', ''))

        # Адрес
        ttk.Label(form_frame, text="Адрес:").grid(row=3, column=0, sticky="w", pady=5)
        address_frame = ttk.Frame(form_frame)
        address_frame.grid(row=3, column=1, sticky="ew", padx=10, pady=5)

        self.address_entry = ttk.Entry(address_frame)
        self.address_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.address_entry.insert(0, self.task_data.get('address', ''))

        ttk.Button(address_frame, text="Выбрать",
                  command=self.select_address).pack(side=tk.RIGHT, padx=(5, 0))

        # Контрагент
        ttk.Label(form_frame, text="Контрагент:").grid(row=4, column=0, sticky="w", pady=5)
        self.contractor_entry = ttk.Entry(form_frame, width=40)
        self.contractor_entry.grid(row=4, column=1, sticky="ew", padx=10, pady=5)
        self.contractor_entry.insert(0, self.task_data.get('contractor', ''))

        # Описание
        ttk.Label(form_frame, text="Описание:").grid(row=5, column=0, sticky="nw", pady=5)
        self.description_text = tk.Text(form_frame, width=40, height=6)
        self.description_text.grid(row=5, column=1, sticky="ew", padx=10, pady=5)
        self.description_text.insert("1.0", self.task_data.get('description', ''))

        # Выполнено
        self.completed_var = tk.BooleanVar(value=self.task_data.get('completed', False))
        ttk.Checkbutton(form_frame, text="Выполнено",
                       variable=self.completed_var).grid(row=6, column=1, sticky="w", padx=10, pady=5)

        # Настройка растягивания
        form_frame.columnconfigure(1, weight=1)

        # Кнопки
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        ttk.Button(button_frame, text="Сохранить", command=self.save_task).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Отмена", command=self.cancel).pack(side=tk.RIGHT, padx=5)

        # Фокус на первое поле
        self.date_entry.focus()

    def select_address(self):
        """Выбор адреса (заглушка)"""
        ModernMessageBox.show_info(self.dialog, "Выбор адреса", "Функция выбора адреса будет добавлена позже")

    def save_task(self):
        """Сохраняет задачу"""
        # Проверяем обязательные поля
        if not self.task_entry.get().strip():
            ModernMessageBox.show_warning(self.dialog, "Предупреждение", "Введите название задачи")
            return

        # Собираем данные
        self.result = {
            'date': self.date_entry.get().strip(),
            'task': self.task_entry.get().strip(),
            'driver': self.driver_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'contractor': self.contractor_entry.get().strip(),
            'description': self.description_text.get("1.0", tk.END).strip(),
            'completed': self.completed_var.get()
        }

        self.dialog.destroy()

    def cancel(self):
        """Отменяет создание/редактирование"""
        self.result = None
        self.dialog.destroy()
