import tkinter as tk
from tkinter import ttk
from child_window import ChildWindow

class MainWindow(tk.Frame):
    def __init__(self, master=None):
        super().__init__(master)
        self.master = master
        self.master.title("Главное окно")
        self.master.geometry("600x400")
        self.pack()
        self.create_widgets()

    def create_widgets(self):
        # Кнопка для открытия дочернего окна
        self.open_child_btn = ttk.Button(
            self, 
            text="Открыть браузер", 
            command=self.open_child_window
        )
        self.open_child_btn.pack(pady=20)

        # Другие элементы (метки, поля ввода и т.д.)
        self.label = ttk.Label(self, text="Добро пожаловать!")
        self.label.pack()

    def open_child_window(self):
        """Создаёт и открывает дочернее окно."""
        child = tk.<PERSON>level(self.master)
        ChildWindow(child)  # Передаём Toplevel в класс дочернего окна