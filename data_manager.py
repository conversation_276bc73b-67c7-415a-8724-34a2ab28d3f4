"""
Модуль управления данными
Содержит функции для работы с Excel файлами, кэшем геокодирования и сохранением данных
"""

import os
import pandas as pd
from typing import Dict, List, Tuple, Any
from tkinter import messagebox


class DataManager:
    """Класс для управления данными приложения"""

    def __init__(self):
        self.geocode_cache = {}
        self.address_values = {}
        self.drivers_registry = {}
        self.tasks = {}
        self.saved_drivers = {}
        self.drivers_schedule = {
            'Понедельник': [],
            'Вторник': [],
            'Среда': [],
            'Четверг': [],
            'Пятница': [],
            'Суббота': [],
            'Воскресенье': []
        }
        self.next_task_id = 1

    def load_geocode_cache(self) -> None:
        """Загрузка кэша геокодированных адресов из Excel файла"""
        cache_file = "geocode_cache.xlsx"

        if not os.path.exists(cache_file):
            print("Файл кэша не найден, будет создан новый")
            return

        try:
            df = pd.read_excel(cache_file)

            # Проверяем наличие необходимых столбцов
            if all(col in df.columns for col in ['Адрес', 'Широта', 'Долгота']):
                for _, row in df.iterrows():
                    address = row['Адрес']
                    lat = row['Широта']
                    lng = row['Долгота']

                    if pd.notna(address) and pd.notna(lat) and pd.notna(lng):
                        self.geocode_cache[address] = (float(lat), float(lng))

                print(f"Загружено {len(self.geocode_cache)} адресов из кэша")
            else:
                print("Некорректный формат файла кэша")
        except Exception as e:
            print(f"Ошибка при загрузке кэша: {e}")
            messagebox.showwarning("Предупреждение", f"Не удалось загрузить кэш адресов:\n{str(e)}")

    def save_geocode_cache(self) -> None:
        """Сохранение кэша геокодированных адресов в Excel файл"""
        cache_file = "geocode_cache.xlsx"

        try:
            # Создаем DataFrame из кэша
            data = []
            for address, (lat, lng) in self.geocode_cache.items():
                data.append({
                    'Адрес': address,
                    'Широта': lat,
                    'Долгота': lng
                })

            if not data:
                print("Кэш пуст, нечего сохранять")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(cache_file, index=False)
            print(f"Сохранено {len(data)} адресов в кэш")
        except Exception as e:
            print(f"Ошибка при сохранении кэша: {e}")
            messagebox.showerror("Ошибка", f"Не удалось сохранить кэш адресов:\n{str(e)}")

    def load_address_values(self) -> None:
        """Загружает значения адресов из Excel файла"""
        values_file = "address_values.xlsx"

        if not os.path.exists(values_file):
            print("Файл значений адресов не найден")
            return

        try:
            df = pd.read_excel(values_file)

            # Проверяем наличие необходимых столбцов
            required_columns = ['Адрес', 'Сумма', 'Вес']
            if all(col in df.columns for col in required_columns):
                for _, row in df.iterrows():
                    address = row['Адрес']
                    sum_value = row['Сумма'] if pd.notna(row['Сумма']) else 0.0
                    weight_value = row['Вес'] if pd.notna(row['Вес']) else 0.0

                    if pd.notna(address):
                        self.address_values[address] = {
                            'sum': float(sum_value),
                            'weight': float(weight_value)
                        }

                print(f"Загружены значения для {len(self.address_values)} адресов")
            else:
                print("Некорректный формат файла значений адресов")
        except Exception as e:
            print(f"Ошибка при загрузке значений адресов: {e}")

    def save_address_values(self) -> None:
        """Сохраняет значения адресов в Excel файл"""
        values_file = "address_values.xlsx"

        try:
            # Создаем DataFrame из значений
            data = []
            for address, values in self.address_values.items():
                data.append({
                    'Адрес': address,
                    'Сумма': values.get('sum', 0.0),
                    'Вес': values.get('weight', 0.0)
                })

            if not data:
                print("Нет значений для сохранения")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(values_file, index=False)
            print(f"Сохранено значений для {len(data)} адресов")
        except Exception as e:
            print(f"Ошибка при сохранении значений адресов: {e}")

    def load_drivers_registry(self) -> None:
        """Загружает реестр водителей из Excel файла"""
        registry_file = "drivers_registry.xlsx"

        if not os.path.exists(registry_file):
            print("Файл реестра водителей не найден")
            return

        try:
            df = pd.read_excel(registry_file)

            # Проверяем наличие необходимых столбцов
            if 'Водитель' in df.columns:
                for _, row in df.iterrows():
                    driver_name = row['Водитель']
                    region = row.get('Регион', '') if pd.notna(row.get('Регион', '')) else ''

                    if pd.notna(driver_name):
                        self.drivers_registry[driver_name] = {'region': region}

                print(f"Загружено {len(self.drivers_registry)} водителей из реестра")
            else:
                print("Некорректный формат файла реестра водителей")
        except Exception as e:
            print(f"Ошибка при загрузке реестра водителей: {e}")

    def save_drivers_registry(self) -> None:
        """Сохраняет реестр водителей в Excel файл"""
        registry_file = "drivers_registry.xlsx"

        try:
            # Создаем DataFrame из реестра
            data = []
            for driver_name, info in self.drivers_registry.items():
                data.append({
                    'Водитель': driver_name,
                    'Регион': info.get('region', '')
                })

            if not data:
                print("Реестр водителей пуст")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(registry_file, index=False)
            print(f"Сохранено {len(data)} водителей в реестр")
        except Exception as e:
            print(f"Ошибка при сохранении реестра водителей: {e}")

    def load_tasks(self) -> None:
        """Загружает задачи из Excel файла"""
        tasks_file = "tasks.xlsx"

        if not os.path.exists(tasks_file):
            print("Файл задач не найден")
            return

        try:
            df = pd.read_excel(tasks_file)

            # Проверяем наличие необходимых столбцов
            required_columns = ['ID', 'Дата', 'Задача', 'Водитель', 'Описание', 'Выполнено']
            if all(col in df.columns for col in required_columns):
                max_id = 0
                for _, row in df.iterrows():
                    task_id = int(row['ID'])
                    max_id = max(max_id, task_id)

                    self.tasks[task_id] = {
                        'date': row['Дата'] if pd.notna(row['Дата']) else '',
                        'task': row['Задача'] if pd.notna(row['Задача']) else '',
                        'driver': row['Водитель'] if pd.notna(row['Водитель']) else '',
                        'description': row['Описание'] if pd.notna(row['Описание']) else '',
                        'completed': bool(row['Выполнено']) if pd.notna(row['Выполнено']) else False,
                        'address': row.get('Адрес', '') if pd.notna(row.get('Адрес', '')) else '',
                        'contractor': row.get('Контрагент', '') if pd.notna(row.get('Контрагент', '')) else ''
                    }

                self.next_task_id = max_id + 1
                print(f"Загружено {len(self.tasks)} задач из списка")
            else:
                print("Некорректный формат файла задач")
        except Exception as e:
            print(f"Ошибка при загрузке задач: {e}")

    def save_tasks(self) -> None:
        """Сохраняет задачи в Excel файл"""
        tasks_file = "tasks.xlsx"

        try:
            # Создаем DataFrame из задач
            data = []
            for task_id, task_info in self.tasks.items():
                data.append({
                    'ID': task_id,
                    'Дата': task_info.get('date', ''),
                    'Задача': task_info.get('task', ''),
                    'Водитель': task_info.get('driver', ''),
                    'Описание': task_info.get('description', ''),
                    'Выполнено': task_info.get('completed', False),
                    'Адрес': task_info.get('address', ''),
                    'Контрагент': task_info.get('contractor', '')
                })

            if not data:
                print("Нет задач для сохранения")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(tasks_file, index=False)
            print(f"Сохранено {len(data)} задач в список")
        except Exception as e:
            print(f"Ошибка при сохранении задач: {e}")

    def load_schedule(self) -> None:
        """Загружает расписание из Excel файла"""
        schedule_file = "drivers_schedule.xlsx"

        if not os.path.exists(schedule_file):
            print("Файл расписания не найден")
            return

        try:
            # Загружаем все листы
            excel_data = pd.read_excel(schedule_file, sheet_name=None)

            for day, df in excel_data.items():
                if day in self.drivers_schedule:
                    self.drivers_schedule[day] = []

                    # Проверяем наличие необходимых столбцов
                    if 'Регион' in df.columns:
                        for _, row in df.iterrows():
                            region = row['Регион'] if pd.notna(row['Регион']) else ''
                            driver = row.get('Водитель', '') if pd.notna(row.get('Водитель', '')) else ''

                            if region:  # Добавляем только если есть регион
                                self.drivers_schedule[day].append({
                                    'region': region,
                                    'driver': driver
                                })

            print("Расписание загружено из файла drivers_schedule.xlsx")
        except Exception as e:
            print(f"Ошибка при загрузке расписания: {e}")

    def save_schedule(self) -> None:
        """Сохраняет расписание в Excel файл"""
        schedule_file = "drivers_schedule.xlsx"

        try:
            with pd.ExcelWriter(schedule_file, engine='openpyxl') as writer:
                for day, entries in self.drivers_schedule.items():
                    # Создаем DataFrame для каждого дня
                    data = []
                    for entry in entries:
                        data.append({
                            'Регион': entry.get('region', ''),
                            'Водитель': entry.get('driver', '')
                        })

                    if data:
                        df = pd.DataFrame(data)
                        df.to_excel(writer, sheet_name=day, index=False)

            print("Расписание сохранено в файл drivers_schedule.xlsx")
        except Exception as e:
            print(f"Ошибка при сохранении расписания: {e}")

    def get_address_value(self, address: str, value_type: str) -> float:
        """Получает значение (сумма или вес) для адреса"""
        if address in self.address_values:
            return self.address_values[address].get(value_type, 0.0)
        return 0.0

    def set_address_value(self, address: str, value_type: str, value: float) -> None:
        """Устанавливает значение (сумма или вес) для адреса"""
        if address not in self.address_values:
            self.address_values[address] = {'sum': 0.0, 'weight': 0.0}
        self.address_values[address][value_type] = value

    def load_saved_drivers(self) -> None:
        """Загружает сохраненных водителей из Excel файла"""
        drivers_file = "saved_drivers.xlsx"

        if not os.path.exists(drivers_file):
            print("Файл сохраненных водителей не найден")
            return

        try:
            df = pd.read_excel(drivers_file)

            # Проверяем наличие необходимых столбцов
            required_columns = ['Ключ', 'Водитель', 'Цвет', 'Количество', 'Сумма']
            if all(col in df.columns for col in required_columns):
                for _, row in df.iterrows():
                    key = row['Ключ']
                    driver_name = row['Водитель'] if pd.notna(row['Водитель']) else ''
                    color = row['Цвет'] if pd.notna(row['Цвет']) else ''
                    count = int(row['Количество']) if pd.notna(row['Количество']) else 0
                    sum_value = float(row['Сумма']) if pd.notna(row['Сумма']) else 0.0

                    if pd.notna(key):
                        self.saved_drivers[key] = {
                            'driver_name': driver_name,
                            'color': color,
                            'count': count,
                            'sum': sum_value,
                            'addresses': []  # Адреса загружаются отдельно
                        }

                print(f"Загружено {len(self.saved_drivers)} сохраненных водителей")
            else:
                print("Некорректный формат файла сохраненных водителей")
        except Exception as e:
            print(f"Ошибка при загрузке сохраненных водителей: {e}")

    def save_saved_drivers(self) -> None:
        """Сохраняет водителей в Excel файл"""
        drivers_file = "saved_drivers.xlsx"

        try:
            # Создаем DataFrame из сохраненных водителей
            data = []
            for key, driver_info in self.saved_drivers.items():
                data.append({
                    'Ключ': key,
                    'Водитель': driver_info.get('driver_name', ''),
                    'Цвет': driver_info.get('color', ''),
                    'Количество': driver_info.get('count', 0),
                    'Сумма': driver_info.get('sum', 0.0)
                })

            if not data:
                print("Нет сохраненных водителей")
                return

            df = pd.DataFrame(data)

            # Сохраняем в Excel
            df.to_excel(drivers_file, index=False)
            print(f"Сохранено {len(data)} водителей")
        except Exception as e:
            print(f"Ошибка при сохранении водителей: {e}")

    def load_all_data(self) -> None:
        """Загружает все данные из файлов"""
        print("Загрузка данных...")
        self.load_geocode_cache()
        self.load_address_values()
        self.load_drivers_registry()
        self.load_tasks()
        self.load_schedule()
        self.load_saved_drivers()
        print("Загрузка данных завершена")

    def save_all_data(self) -> None:
        """Сохраняет все данные в файлы"""
        print("Сохранение данных...")
        self.save_geocode_cache()
        self.save_address_values()
        self.save_drivers_registry()
        self.save_tasks()
        self.save_schedule()
        self.save_saved_drivers()
        print("Сохранение данных завершено")
